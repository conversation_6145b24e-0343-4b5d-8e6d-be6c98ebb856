## Installation Instructions for Ezz App

This version of the app has a modified package name (rt.tt.temp.app) to avoid conflicts with existing installations.

### If you're experiencing "Package Conflicts" error:

1. The APK in this folder has a different package name and can be installed alongside any existing version.
2. This is NOT an update to any existing app with the package name "rt.tt.temp" - it's a separate installation.
3. You can have both versions installed simultaneously.

### To build a new APK:

#### On Windows:
Run the `build_update_apk.bat` script from the root directory.

#### On macOS/Linux:
Run the `build_update_apk.sh` script from the root directory.

### Technical Details:

- Package Name: rt.tt.temp.app
- Version Code: 5
- Version Name: 0.5

If you need to uninstall the previous version, you can do so through the device's Settings > Apps menu.

@echo off
echo Building new package APK (version 0.5)...
call gradlew :app:assembleDebug

echo.
echo Copying APK to output directory...
mkdir output 2>nul
copy /Y app\build\outputs\apk\debug\app-debug.apk output\ezz-app-v0.5-new-package.apk

echo.
echo New package APK is ready at: output\ezz-app-v0.5-new-package.apk
echo.
echo This APK has a different package name (rt.tt.temp.app) and can be installed alongside the existing app.
echo.

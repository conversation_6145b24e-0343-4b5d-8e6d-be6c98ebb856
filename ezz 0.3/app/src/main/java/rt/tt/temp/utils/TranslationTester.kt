package rt.tt.temp.utils

import android.content.Context
import android.util.Log
import android.widget.Toast
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL

/**
 * Utility class to test translation functionality
 */
class TranslationTester(private val context: Context) {
    private val TAG = "TranslationTester"
    private val API_URL = "https://libretranslate.de/translate"

    /**
     * Test the Libre Translator API with a simple text
     */
    fun testLibreTranslator() {
        CoroutineScope(Dispatchers.Main).launch {
            try {
                // Show a toast that we're testing
                Toast.makeText(context, "Testing Libre Translator...", Toast.LENGTH_SHORT).show()

                // Test Arabic to English
                val arabicText = "مرحبا بالعالم"
                testTranslation(arabicText, "ar", "en")

                // Test English to Arabic
                val englishText = "Hello World"
                testTranslation(englishText, "en", "ar")

                // Test English to Bengali
                testTranslation(englishText, "en", "bn")

                // Show a toast that testing is complete
                Toast.makeText(context, "Testing complete. Check logs.", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                Log.e(TAG, "Test failed: ${e.message}", e)
                Toast.makeText(context, "Test failed: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private suspend fun testTranslation(text: String, sourceLanguage: String, targetLanguage: String) {
        withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Testing direct translation from $sourceLanguage to $targetLanguage: '$text'")

                // Create connection
                val url = URL(API_URL)
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.setRequestProperty("Content-Type", "application/json")
                connection.setRequestProperty("Accept", "application/json")
                connection.doOutput = true
                connection.connectTimeout = 30000 // 30 seconds
                connection.readTimeout = 30000 // 30 seconds

                // Create JSON request body
                val jsonRequest = JSONObject().apply {
                    put("q", text)
                    put("source", sourceLanguage)
                    put("target", targetLanguage)
                    put("format", "text")
                    put("api_key", "") // No API key needed for this instance
                }

                Log.d(TAG, "Request JSON: ${jsonRequest.toString()}")

                // Send request
                val outputStream = connection.outputStream
                val writer = OutputStreamWriter(outputStream)
                writer.write(jsonRequest.toString())
                writer.flush()
                writer.close()

                // Get response
                val responseCode = connection.responseCode
                Log.d(TAG, "Response code: $responseCode")

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    // Read response
                    val inputStream = connection.inputStream
                    val reader = BufferedReader(InputStreamReader(inputStream))
                    val response = StringBuilder()
                    var line: String?
                    while (reader.readLine().also { line = it } != null) {
                        response.append(line)
                    }
                    reader.close()

                    val responseString = response.toString()
                    Log.d(TAG, "Response: $responseString")

                    // Parse JSON response
                    val jsonResponse = JSONObject(responseString)
                    val translatedText = jsonResponse.optString("translatedText", text)

                    // Check if translation was successful
                    if (translatedText.isNotBlank() && translatedText != text) {
                        Log.d(TAG, "Translation successful: '$text' -> '$translatedText'")
                        withContext(Dispatchers.Main) {
                            Toast.makeText(
                                context,
                                "Translated: $text -> $translatedText",
                                Toast.LENGTH_LONG
                            ).show()
                        }
                    } else {
                        Log.w(TAG, "Translation returned same text: '$text'")
                        withContext(Dispatchers.Main) {
                            Toast.makeText(
                                context,
                                "Translation failed: Same text returned",
                                Toast.LENGTH_LONG
                            ).show()
                        }
                    }
                } else {
                    // Read error response
                    val errorStream = connection.errorStream
                    val reader = BufferedReader(InputStreamReader(errorStream))
                    val error = StringBuilder()
                    var line: String?
                    while (reader.readLine().also { line = it } != null) {
                        error.append(line)
                    }
                    reader.close()

                    val errorMessage = "Error: ${error.toString()}"
                    Log.e(TAG, "Translation failed with code: $responseCode, $errorMessage")
                    withContext(Dispatchers.Main) {
                        Toast.makeText(
                            context,
                            errorMessage,
                            Toast.LENGTH_LONG
                        ).show()
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Translation test failed: ${e.message}", e)
                withContext(Dispatchers.Main) {
                    Toast.makeText(
                        context,
                        "Translation test failed: ${e.message}",
                        Toast.LENGTH_LONG
                    ).show()
                }
            }
        }
    }
}

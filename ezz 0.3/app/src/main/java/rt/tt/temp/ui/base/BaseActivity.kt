package rt.tt.temp.ui.base

import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import android.os.Bundle
import android.os.Build
import androidx.activity.ComponentActivity
import androidx.core.view.WindowCompat
import androidx.preference.PreferenceManager
import java.util.Locale

open class BaseActivity : ComponentActivity() {
    private lateinit var activityContext: Context

    override fun attachBaseContext(newBase: Context) {
        val prefs = PreferenceManager.getDefaultSharedPreferences(newBase)
        val languageCode = prefs.getString("app_language", Locale.getDefault().language) ?: "en"
        val locale = Locale(languageCode)
        Locale.setDefault(locale)
        
        val config = Configuration(newBase.resources.configuration)
        config.setLocale(locale)
        activityContext = newBase.createConfigurationContext(config)
        
        super.attachBaseContext(activityContext)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        val prefs = PreferenceManager.getDefaultSharedPreferences(this)
        val languageCode = prefs.getString("app_language", Locale.getDefault().language) ?: "en"
        val locale = Locale(languageCode)
        Locale.setDefault(locale)
        
        val config = Configuration(newConfig)
        config.setLocale(locale)
        createConfigurationContext(config)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val configuration = resources.configuration
        val locale = Locale(PreferenceManager.getDefaultSharedPreferences(this)
            .getString("app_language", Locale.getDefault().language) ?: "en")
        Locale.setDefault(locale)
        configuration.setLocale(locale)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            createConfigurationContext(configuration)
        }
        resources.updateConfiguration(configuration, resources.displayMetrics)
    }

    override fun onResume() {
        super.onResume()
        // Ensure locale is applied when returning to the activity
        val prefs = PreferenceManager.getDefaultSharedPreferences(this)
        val languageCode = prefs.getString("app_language", Locale.getDefault().language) ?: "en"
        val locale = Locale(languageCode)
        Locale.setDefault(locale)
        val config = resources.configuration
        config.setLocale(locale)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            createConfigurationContext(config)
        }
        resources.updateConfiguration(config, resources.displayMetrics)
    }

    override fun getResources(): Resources {
        return if (::activityContext.isInitialized) {
            activityContext.resources
        } else {
            super.getResources()
        }
    }
}
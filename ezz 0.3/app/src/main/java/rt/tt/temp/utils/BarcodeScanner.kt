package rt.tt.temp.utils

import android.content.Context
import android.util.Log
import com.journeyapps.barcodescanner.ScanOptions
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import rt.tt.temp.data.AppDatabase

class BarcodeScanner(
    private val context: Context,
    private val scope: CoroutineScope
) {
    private val TAG = "BarcodeScanner"
    private val database = AppDatabase.getInstance(context)

    private var onProductFound: ((String, String, String) -> Unit)? = null
    private var onProductNotFound: ((String) -> Unit)? = null
    private var onScanError: ((String) -> Unit)? = null

    fun setCallbacks(
        onProductFound: (String, String, String) -> Unit,
        onProductNotFound: (String) -> Unit,
        onScanError: (String) -> Unit
    ) {
        this.onProductFound = onProductFound
        this.onProductNotFound = onProductNotFound
        this.onScanError = onScanError
    }

    fun getScanOptions(): ScanOptions {
        return ScanOptions().apply {
            setDesiredBarcodeFormats(ScanOptions.ALL_CODE_TYPES) // Accept all formats
            setPrompt("Align barcode within the frame")
            setBeepEnabled(true)
            setOrientationLocked(false) // Allow both orientations
            setBarcodeImageEnabled(true)
            setCameraId(0)
            setTimeout(SCAN_TIMEOUT_MS.toLong())
        }
    }

    fun lookupProduct(barcode: String) {
        if (barcode.isBlank()) {
            Log.e(TAG, "Empty barcode received")
            onScanError?.invoke("Invalid barcode")
            return
        }

        Log.d(TAG, "Looking up barcode: $barcode")
        
        scope.launch {
            try {
                // Try to find the product using ProductCatalogDao
                val product = database.productCatalogDao().getProductByBarcode(barcode)
                Log.d(TAG, "Database lookup result for $barcode: $product")

                if (product != null) {
                    Log.d(TAG, "Product found: ${product.productName}")
                    onProductFound?.invoke(
                        product.productName,
                        product.productType,
                        product.barcode
                    )
                } else {
                    // If not found, try with the last 6 digits
                    val last6Digits = if (barcode.length >= 6) {
                        barcode.takeLast(6)
                    } else {
                        barcode
                    }
                    Log.d(TAG, "Trying last 6 digits: $last6Digits")
                    val productBy6Digits = database.productCatalogDao().getProductBySixDigits(last6Digits)
                    Log.d(TAG, "6-digit lookup result: $productBy6Digits")
                    
                    if (productBy6Digits != null) {
                        Log.d(TAG, "Product found by last 6 digits: ${productBy6Digits.productName}")
                        onProductFound?.invoke(
                            productBy6Digits.productName,
                            productBy6Digits.productType,
                            productBy6Digits.barcode
                        )
                    } else {
                        Log.d(TAG, "No product found for barcode: $barcode")
                        onProductNotFound?.invoke(barcode)
                    }
                }
            } catch (e: Exception) {
                val errorMessage = when (e) {
                    is NumberFormatException -> "Invalid barcode format"
                    is IllegalArgumentException -> e.message ?: "Invalid barcode"
                    else -> "Error looking up product: ${e.message}"
                }
                Log.e(TAG, "Error in lookupProduct: $errorMessage", e)
                onScanError?.invoke(errorMessage)
            }
        }
    }

    companion object {
        const val SCAN_TIMEOUT_MS = 20000
    }
}

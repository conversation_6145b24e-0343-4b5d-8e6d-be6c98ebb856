package rt.tt.temp.ui.screens

import android.app.Activity
import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material.icons.filled.QrCodeScanner
import androidx.compose.material.icons.outlined.DateRange
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import rt.tt.temp.data.AppDatabase
import rt.tt.temp.utils.BarcodeScanner
import java.util.*
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.compose.rememberLauncherForActivityResult
import com.journeyapps.barcodescanner.ScanContract
import com.journeyapps.barcodescanner.ScanOptions
import android.app.DatePickerDialog
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import kotlinx.coroutines.delay
import android.util.Log
import androidx.compose.runtime.rememberCoroutineScope
import kotlinx.coroutines.launch
import rt.tt.temp.data.ProductCatalog  // Add your actual package path
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.text.SimpleDateFormat
import androidx.navigation.compose.rememberNavController
import androidx.navigation.NavController
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import rt.tt.temp.R
import rt.tt.temp.data.ProductCatalogImporter

val LocalNavController = staticCompositionLocalOf<NavController> { 
    error("No NavController provided") 
}

private fun isValidBatchFormat(batch: String): Boolean {
    return batch.matches(Regex("\\d{2}:\\d{2}"))
}

private fun formatBatchInput(input: String): String {
    val digitsOnly = input.filter { it.isDigit() }
    return when {
        digitsOnly.length >= 4 -> "${digitsOnly.take(2)}:${digitsOnly.substring(2, 4)}"
        else -> digitsOnly
    }
}

private fun isValidDateFormat(dateStr: String, includeDay: Boolean): Boolean {
    if (dateStr.isEmpty()) return false
    return if (includeDay) {
        if (dateStr.length != 8) return false
        val day = dateStr.take(2).toIntOrNull() ?: return false
        val month = dateStr.substring(2, 4).toIntOrNull() ?: return false
        val year = dateStr.takeLast(4).toIntOrNull() ?: return false
        
        if (month !in 1..12) return false
        
        val maxDays = when (month) {
            2 -> if (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0)) 29 else 28
            4, 6, 9, 11 -> 30
            else -> 31
        }
        day in 1..maxDays
    } else {
        if (dateStr.length != 6) return false
        val month = dateStr.take(2).toIntOrNull() ?: return false
        dateStr.takeLast(4).toIntOrNull() != null && month in 1..12
    }
}

private fun isValidMonthYear(dateStr: String): Boolean {
    if (dateStr.length != 6) return false
    val month = dateStr.take(2).toIntOrNull() ?: return false
    return month in 1..12
}

private fun isValidDayMonthYear(dateStr: String): Boolean {
    if (dateStr.length != 8) return false
    val day = dateStr.take(2).toIntOrNull() ?: return false
    val month = dateStr.substring(2, 4).toIntOrNull() ?: return false
    return day in 1..31 && month in 1..12
}

private fun formatDateInput(input: String, includeDay: Boolean): String {
    val digitsOnly = input.filter { it.isDigit() }
    return digitsOnly.take(if (includeDay) 8 else 6)
}

private fun formatDateWithSlashes(input: String, includeDay: Boolean): String {
    return when {
        includeDay && input.length == 8 -> {
            "${input.take(2)}/${input.substring(2, 4)}/${input.takeLast(4)}"
        }
        !includeDay && input.length == 6 -> {
            "${input.take(2)}/${input.takeLast(4)}"
        }
        else -> input
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NewEntryScreen(
    database: AppDatabase,
    onSave: suspend (String, String, String, String, String, Int, Date, Date) -> Unit
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val dateFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
    
    var showError by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }
    var showSnackbar by remember { mutableStateOf(false) }
    var snackbarMessage by remember { mutableStateOf("") }
    
    var includeDayInDate by remember { mutableStateOf(false) }
    var branchName by remember { mutableStateOf("") }
    var productName by remember { mutableStateOf("") }
    var productType by remember { mutableStateOf("") }
    var barcode by remember { mutableStateOf("") }
    var batch by remember { mutableStateOf("") }
    var quantity by remember { mutableStateOf("") }
    var mfgDateString by remember { mutableStateOf("") }
    var expDateString by remember { mutableStateOf("") }
    var expanded by remember { mutableStateOf(false) }
    // --- Auto-fill logic for barcode ---
    LaunchedEffect(barcode) {
        if (barcode.isNotBlank()) {
            Log.d("NewEntryScreen", "Looking up barcode: $barcode")
            val product = database.productCatalogDao().getProductByBarcode(barcode)
            Log.d("NewEntryScreen", "Product found: $product")
            if (product != null) {
                productName = product.productName
                productType = product.productType
                Log.d("NewEntryScreen", "Auto-filled: name=${product.productName}, type=${product.productType}")
            }
        }
    }
    // Add missing state variables
    var showAddProductDialog by remember { mutableStateOf(false) }
    var pendingBarcode by remember { mutableStateOf("") }
    var pendingSaveCallback by remember { mutableStateOf<((String, String) -> Unit)?>(null) }

    fun showDatePicker(onDateSelected: (Int, Int, Int) -> Unit) {
        val calendar = Calendar.getInstance()
        val datePickerDialog = DatePickerDialog(
            context,
            { _, year, month, dayOfMonth ->
                onDateSelected(year, month, dayOfMonth)
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        )
        datePickerDialog.show()
    }

    // Pre-load all strings that will be used in non-composable contexts
    val strings = remember(context) {
        mapOf(
            "branch_required" to context.getString(R.string.error_branch_required),
            "product_required" to context.getString(R.string.error_product_name_required),
            "barcode_required" to context.getString(R.string.error_barcode_required),
            "invalid_batch" to context.getString(R.string.error_invalid_batch_format),
            "invalid_mfg_date_with_day" to context.getString(R.string.error_invalid_mfg_date_format_with_day),
            "invalid_mfg_date_without_day" to context.getString(R.string.error_invalid_mfg_date_format_without_day),
            "invalid_exp_date_with_day" to context.getString(R.string.error_invalid_exp_date_format_with_day),
            "invalid_exp_date_without_day" to context.getString(R.string.error_invalid_exp_date_format_without_day),
            "invalid_date_format" to context.getString(R.string.error_invalid_date_format),
            "expiry_after_manufacturing" to context.getString(R.string.error_expiry_after_manufacturing),
            "quantity_greater_than_zero" to context.getString(R.string.error_quantity_greater_than_zero),
            "success_product_saved" to context.getString(R.string.success_product_saved)
        )
    }

    // Branch names list
    val branchNames = remember {
        listOf(
            context.getString(R.string.branch_warehouse),
            context.getString(R.string.branch_jumlah),
            context.getString(R.string.branch_bairut),
            context.getString(R.string.branch_shifa),
            context.getString(R.string.branch_qafer),
            context.getString(R.string.branch_madain),
            context.getString(R.string.branch_aja),
            context.getString(R.string.branch_baqqa)
        )
    }

    fun validateAndShowError(errorKey: String) {
        showError = true
        errorMessage = strings[errorKey] ?: ""
    }

    fun handleSave() {
        scope.launch {
            try {
                // Validate required fields
                when {
                    branchName.isEmpty() -> {
                        validateAndShowError("branch_required")
                        return@launch
                    }
                    productName.isEmpty() -> {
                        validateAndShowError("product_required")
                        return@launch
                    }
                    barcode.isEmpty() -> {
                        validateAndShowError("barcode_required")
                        return@launch
                    }
                }

                // Parse dates
                val formattedMfgDate = if (includeDayInDate) {
                    "${mfgDateString.take(2)}/${mfgDateString.substring(2,4)}/${mfgDateString.takeLast(4)}"
                } else {
                    "01/${mfgDateString.take(2)}/${mfgDateString.takeLast(4)}"
                }
                
                val formattedExpDate = if (includeDayInDate) {
                    "${expDateString.take(2)}/${expDateString.substring(2,4)}/${expDateString.takeLast(4)}"
                } else {
                    "01/${expDateString.take(2)}/${expDateString.takeLast(4)}"
                }

                val mfgDate = try {
                    dateFormat.parse(formattedMfgDate)
                } catch (e: Exception) {
                    null
                }

                val expDate = try {
                    dateFormat.parse(formattedExpDate)
                } catch (e: Exception) {
                    null
                }

                if (mfgDate == null || expDate == null) {
                    validateAndShowError("invalid_date_format")
                    return@launch
                }

                if (expDate.before(mfgDate)) {
                    validateAndShowError("expiry_after_manufacturing")
                    return@launch
                }

                val quantityInt = quantity.toIntOrNull() ?: 0
                if (quantityInt <= 0) {
                    validateAndShowError("quantity_greater_than_zero")
                    return@launch
                }

                // Save product
                val productCatalog = ProductCatalog(
                    barcode = barcode,
                    productName = productName,
                    productType = productType
                )
                database.productCatalogDao().insertProduct(productCatalog)

                onSave(
                    branchName,
                    productName,
                    productType,
                    barcode,
                    formatBatchInput(batch),
                    quantityInt,
                    mfgDate,
                    expDate
                )

                showSnackbar = true
                snackbarMessage = strings["success_product_saved"] ?: ""

                // Reset form
                branchName = ""
                productName = ""
                productType = ""
                barcode = ""
                batch = ""
                quantity = ""
                mfgDateString = ""
                expDateString = ""

            } catch (e: Exception) {
                validateAndShowError("invalid_date_format")
            }
        }
    }

    val barcodeScanner = remember {
        BarcodeScanner(context, scope).apply {
            setCallbacks(
                onProductFound = { name, type, code ->
                    productName = name
                    productType = type
                    barcode = code
                    showSnackbar = true
                    snackbarMessage = "Product found!"
                },
                onProductNotFound = { scannedBarcode ->
                    barcode = scannedBarcode
                    pendingBarcode = scannedBarcode
                    showAddProductDialog = true
                    productName = ""
                    productType = ""
                },
                onScanError = { error ->
                    showSnackbar = true
                    snackbarMessage = error
                }
            )
        }
    }

    val barcodeLauncher = rememberLauncherForActivityResult(ScanContract()) { result ->
        result.contents?.let { scannedBarcode ->
            barcodeScanner.lookupProduct(scannedBarcode)
        }
    }

    suspend fun reloadProductCatalog() {
        try {
            val importer = ProductCatalogImporter(database.productCatalogDao())
            importer.importProductCatalog()
            
            // Verify the specific product
            val product = database.productCatalogDao().getProductByBarcode("6281064110025")
            Log.d("NewEntryScreen", "Verification - Product 6281064110025: $product")
        } catch (e: Exception) {
            Log.e("NewEntryScreen", "Failed to reload product catalog", e)
        }
    }

    // Call this in your initialization
    LaunchedEffect(Unit) {
        reloadProductCatalog()
    }

    Scaffold { paddingValues ->
        Box(modifier = Modifier.fillMaxSize()) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp)
            ) {
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .verticalScroll(rememberScrollState())
                        .padding(horizontal = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Spacer(modifier = Modifier.height(8.dp))

                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surface
                        )
                    ) {
                        Column(
                            modifier = Modifier
                                .padding(16.dp)
                                .fillMaxWidth()
                        ) {
                            Text(
                                text = stringResource(R.string.branch_information),
                                style = MaterialTheme.typography.titleMedium,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )
                            
                            ExposedDropdownMenuBox(
                                expanded = expanded,
                                onExpandedChange = { expanded = !expanded }
                            ) {
                                OutlinedTextField(
                                    value = branchName,
                                    onValueChange = {},
                                    readOnly = true,
                                    label = { Text(stringResource(R.string.branch_name)) },
                                    trailingIcon = {
                                        ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
                                    },
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .menuAnchor()
                                )

                                ExposedDropdownMenu(
                                    expanded = expanded,
                                    onDismissRequest = { expanded = false }
                                ) {
                                    branchNames.forEach { branch ->
                                        DropdownMenuItem(
                                            text = { Text(branch) },
                                            onClick = {
                                                branchName = branch
                                                expanded = false
                                            }
                                        )
                                    }
                                }
                            }
                        }
                    }

                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surface
                        )
                    ) {
                        Column(
                            modifier = Modifier
                                .padding(16.dp)
                                .fillMaxWidth()
                        ) {
                            Text(
                                text = stringResource(R.string.product_information),
                                style = MaterialTheme.typography.titleMedium,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )

                            OutlinedTextField(
                                value = productName,
                                onValueChange = { productName = it },
                                label = { Text(stringResource(R.string.product_name)) },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
                            )

                            OutlinedTextField(
                                value = productType,
                                onValueChange = { productType = it },
                                label = { Text(stringResource(R.string.product_type)) },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
                            )

                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                OutlinedTextField(
                                    value = barcode,
                                    onValueChange = { barcode = it },
                                    label = { Text(stringResource(R.string.barcode)) },
                                    modifier = Modifier.weight(1f),
                                    keyboardOptions = KeyboardOptions(
                                        keyboardType = KeyboardType.Number,
                                        imeAction = ImeAction.Next
                                    )
                                )
                                FilledIconButton(
                                    onClick = { 
                                        val options = barcodeScanner.getScanOptions()
                                        barcodeLauncher.launch(options)
                                    }
                                ) {
                                    Icon(Icons.Default.QrCodeScanner, "Scan Barcode")
                                }
                            }

                            OutlinedTextField(
                                value = formatBatchInput(batch),
                                onValueChange = { input ->
                                    val digitsOnly = input.filter { it.isDigit() }
                                    if (digitsOnly.length <= 4) {
                                        batch = digitsOnly
                                    }
                                },
                                label = { Text(stringResource(R.string.batch)) },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                keyboardOptions = KeyboardOptions(
                                    keyboardType = KeyboardType.Number,
                                    imeAction = ImeAction.Next
                                )
                            )

                            OutlinedTextField(
                                value = quantity,
                                onValueChange = { if (it.all { char -> char.isDigit() }) quantity = it },
                                label = { Text(stringResource(R.string.quantity)) },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                keyboardOptions = KeyboardOptions(
                                    keyboardType = KeyboardType.Number,
                                    imeAction = ImeAction.Next
                                )
                            )
                        }
                    }

                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surface
                        )
                    ) {
                        Column(
                            modifier = Modifier
                                .padding(16.dp)
                                .fillMaxWidth()
                        ) {
                            Text(
                                text = stringResource(R.string.date_information),
                                style = MaterialTheme.typography.titleMedium,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )

                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 8.dp),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(stringResource(R.string.include_day))
                                Switch(
                                    checked = includeDayInDate,
                                    onCheckedChange = { includeDayInDate = it }
                                )
                            }

                            OutlinedTextField(
                                value = formatDateWithSlashes(mfgDateString, includeDayInDate),
                                onValueChange = { input ->
                                    val digitsOnly = input.filter { it.isDigit() }
                                    if (includeDayInDate && digitsOnly.length <= 8) {
                                        mfgDateString = digitsOnly
                                    } else if (!includeDayInDate && digitsOnly.length <= 6) {
                                        mfgDateString = digitsOnly
                                    }
                                },
                                label = { Text(if (includeDayInDate) stringResource(R.string.mfg_date_with_day) else stringResource(R.string.mfg_date_without_day)) },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                keyboardOptions = KeyboardOptions(
                                    keyboardType = KeyboardType.Number,
                                    imeAction = ImeAction.Next
                                ),
                                trailingIcon = {
                                    IconButton(onClick = {
                                        showDatePicker { year: Int, month: Int, dayOfMonth: Int ->
                                            mfgDateString = if (includeDayInDate) {
                                                String.format("%02d%02d%04d", dayOfMonth, month + 1, year)
                                            } else {
                                                String.format("%02d%04d", month + 1, year)
                                            }
                                        }
                                    }) {
                                        Icon(Icons.Outlined.DateRange, "Select date")
                                    }
                                }
                            )

                            OutlinedTextField(
                                value = formatDateWithSlashes(expDateString, includeDayInDate),
                                onValueChange = { input ->
                                    val digitsOnly = input.filter { it.isDigit() }
                                    if (includeDayInDate && digitsOnly.length <= 8) {
                                        expDateString = digitsOnly
                                    } else if (!includeDayInDate && digitsOnly.length <= 6) {
                                        expDateString = digitsOnly
                                    }
                                },
                                label = { Text(if (includeDayInDate) stringResource(R.string.exp_date_with_day) else stringResource(R.string.exp_date_without_day)) },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                keyboardOptions = KeyboardOptions(
                                    keyboardType = KeyboardType.Number,
                                    imeAction = ImeAction.Done
                                ),
                                trailingIcon = {
                                    IconButton(onClick = {
                                        showDatePicker { year: Int, month: Int, dayOfMonth: Int ->
                                            val calendar = Calendar.getInstance()
                                            calendar.set(year, month, dayOfMonth)
                                            val date = calendar.time

                                            val mfgDate = try {
                                                val formattedMfgDate = if (includeDayInDate) {
                                                    "${mfgDateString.take(2)}/${mfgDateString.substring(2, 4)}/${mfgDateString.takeLast(4)}"
                                                } else {
                                                    "01/${mfgDateString.take(2)}/${mfgDateString.takeLast(4)}"
                                                }
                                                dateFormat.parse(formattedMfgDate)
                                            } catch (e: Exception) {
                                                null
                                            }

                                            if (mfgDate != null && date.before(mfgDate)) {
                                                showError = true
                                                errorMessage = context.getString(R.string.error_expiry_after_manufacturing)
                                            } else {
                                                expDateString = if (includeDayInDate) {
                                                    String.format("%02d%02d%04d", dayOfMonth, month + 1, year)
                                                } else {
                                                    String.format("%02d%04d", month + 1, year)
                                                }
                                            }
                                        }
                                    }) {
                                        Icon(Icons.Outlined.DateRange, "Select date")
                                    }
                                }
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))
                }

                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    shadowElevation = 8.dp,
                    color = MaterialTheme.colorScheme.surface
                ) {
                    Column(
                        modifier = Modifier
                            .padding(16.dp)
                            .fillMaxWidth()
                    ) {
                        Button(
                            onClick = { handleSave() },
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text(stringResource(R.string.save))
                        }
                    }
                }
            }

            if (showAddProductDialog) {
                AlertDialog(
                    onDismissRequest = {
                        showAddProductDialog = false
                        pendingSaveCallback = null
                        productName = ""
                        productType = ""
                    },
                    title = { Text(stringResource(R.string.add_new_product)) },
                    text = {
                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Text("${stringResource(R.string.barcode)}: $pendingBarcode")
                            OutlinedTextField(
                                value = productName,
                                onValueChange = { productName = it },
                                label = { Text(stringResource(R.string.product_name)) },
                                modifier = Modifier.fillMaxWidth()
                            )
                            OutlinedTextField(
                                value = productType,
                                onValueChange = { productType = it },
                                label = { Text(stringResource(R.string.product_type)) },
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    },
                    confirmButton = {
                        Button(
                            onClick = {
                                if (productName.isNotBlank()) {
                                    pendingSaveCallback?.invoke(productName, productType)
                                    barcode = pendingBarcode
                                    showAddProductDialog = false
                                    pendingSaveCallback = null
                                } else {
                                    showSnackbar = true
                                    snackbarMessage = context.getString(R.string.error_product_name_required)
                                }
                            }
                        ) {
                            Text(stringResource(R.string.save))
                        }
                    },
                    dismissButton = {
                        TextButton(
                            onClick = {
                                showAddProductDialog = false
                                pendingSaveCallback = null
                                productName = ""
                                productType = ""
                            }
                        ) {
                            Text(stringResource(R.string.cancel))
                        }
                    }
                )
            }

            if (showError) {
                AlertDialog(
                    onDismissRequest = { showError = false },
                    title = { Text(stringResource(R.string.error)) },
                    text = { Text(errorMessage) },
                    confirmButton = {
                        TextButton(onClick = { showError = false }) {
                            Text(stringResource(R.string.ok))
                        }
                    }
                )
            }

            if (showSnackbar) {
                LaunchedEffect(Unit) {
                    delay(2000)
                    showSnackbar = false
                }
                Snackbar(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(snackbarMessage)
                }
            }
        }
    }
}

@Composable
private fun ShowAddProductDialog(
    showDialog: Boolean,
    pendingBarcode: String,
    productName: String,
    productType: String,
    onProductNameChange: (String) -> Unit,
    onProductTypeChange: (String) -> Unit,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    if (showDialog) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = { Text(stringResource(R.string.add_new_product)) },
            text = {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text("${stringResource(R.string.barcode)}: $pendingBarcode")
                    OutlinedTextField(
                        value = productName,
                        onValueChange = onProductNameChange,
                        label = { Text(stringResource(R.string.product_name)) },
                        modifier = Modifier.fillMaxWidth()
                    )
                    OutlinedTextField(
                        value = productType,
                        onValueChange = onProductTypeChange,
                        label = { Text(stringResource(R.string.product_type)) },
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            },
            confirmButton = {
                Button(onClick = onConfirm) {
                    Text(stringResource(R.string.save))
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }
}

@Composable
private fun ShowErrorDialog(
    show: Boolean,
    message: String,
    onDismiss: () -> Unit
) {
    if (show) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = { Text(stringResource(R.string.error)) },
            text = { Text(message) },
            confirmButton = {
                TextButton(onClick = onDismiss) {
                    Text(stringResource(R.string.ok))
                }
            }
        )
    }
}

@Composable
private fun ShowSnackbar(
    show: Boolean,
    message: String,
    onDismiss: () -> Unit
) {
    if (show) {
        LaunchedEffect(Unit) {
            delay(2000)
            onDismiss()
        }
        Snackbar(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(message)
        }
    }
}

@Composable
private fun ValidationErrors(errors: Map<String, String>) {
    Column {
        errors.forEach { (field, error) ->
            Text(
                text = "$field: $error",
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

package rt.tt.temp.data

import android.content.Context
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import rt.tt.temp.utils.SecurityUtils
import java.util.*

private val Context.dataStore by preferencesDataStore(name = "user_prefs")


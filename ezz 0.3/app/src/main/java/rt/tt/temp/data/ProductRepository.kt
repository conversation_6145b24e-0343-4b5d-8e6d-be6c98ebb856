package rt.tt.temp.data

import kotlinx.coroutines.flow.Flow

class ProductRepository(private val productDao: ProductDao) {
    fun getAllExpiredProducts(): Flow<List<Product>> {
        val currentDate = System.currentTimeMillis()
        return productDao.getAllExpiredProducts(currentDate)
    }

    suspend fun getTotalProductCount(): Int {
        val currentDate = System.currentTimeMillis()
        return productDao.getTotalProductCount(currentDate)
    }

    fun getNearestExpiringProducts(): Flow<List<Product>> {
        val currentDate = System.currentTimeMillis()
        return productDao.getNearestExpiringProducts(currentDate)
    }

    // ... other methods ...
}
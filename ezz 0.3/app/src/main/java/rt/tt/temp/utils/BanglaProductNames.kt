package rt.tt.temp.utils

/**
 * Class containing accurate translations of product names to Bangla
 * This serves as a local translation database for product names
 */
object BanglaProductNames {
    // Map of English product names to their Bangla translations
    private val englishToBangla = mapOf(
        // Spices
        "Turmeric Spices" to "হলুদ মশলা",
        "Hot Pepper Spices Box" to "ঝাল মরিচ মশলা বাক্স",
        "Mixed Spices Box" to "মিশ্র মশলা বাক্স",
        "Indonesian Cloves 180g" to "ইন্দোনেশিয়ান লবঙ্গ ১৮০ গ্রাম",
        "Farha Cardamom 250g" to "ফারহা এলাচ ২৫০ গ্রাম",
        
        // Cleaning Products
        "Large Clorox" to "বড় ক্লোরক্স",
        "Pif Paf Powder" to "পিফ পাফ পাউডার",
        "Regular Tide 5kg" to "সাধারণ টাইড ৫ কেজি",
        "Automatic Tide 5kg" to "অটোমেটিক টাইড ৫ কেজি",
        
        // Oils
        "Shams Oil 1.5L" to "শামস তেল ১.৫ লিটার",
        "Al Jouf Olive Oil 250ml" to "আল জৌফ জৈতুন তেল ২৫০ মিলি",
        
        // Tea and Coffee
        "Al Kabous Yemeni Alaqi Tea 100" to "আল কাবুস ইয়েমেনি আলাকি চা ১০০",
        "Lipton Alaqi Tea 100-36" to "লিপটন আলাকি চা ১০০-৩৬",
        "999 Tea" to "৯৯৯ চা",
        "Al Kabous Tea 227g" to "আল কাবুস চা ২২৭ গ্রাম",
        "Lipton Yellow Label Tea 36×100" to "লিপটন ইয়েলো লেবেল চা ৩৬×১০০",
        "Nescafe Powder - 190g" to "নেসক্যাফে পাউডার - ১৯০ গ্রাম",
        "Rawaq Coffee 500g" to "রাওয়াক কফি ৫০০ গ্রাম",
        
        // Sugar
        "Al Osra Fine Sugar 5kg" to "আল ওসরা মিহি চিনি ৫ কেজি",
        "Al Osra Coarse Sugar 5kg" to "আল ওসরা মোটা চিনি ৫ কেজি",
        "Al Israa Coarse Sugar 5kg" to "আল ইসরা মোটা চিনি ৫ কেজি",
        
        // Noodles and Pasta
        "Indomie Chicken Cup 60g" to "ইন্ডোমি চিকেন কাপ ৬০ গ্রাম",
        "Indomie Vegetable Pack 5 pieces" to "ইন্ডোমি সবজি প্যাক ৫ পিস",
        "Perfetto Spaghetti 450g" to "পারফেটো স্পাগেটি ৪৫০ গ্রাম",
        
        // Flour
        "Foam White Flour 1kg" to "ফোম সাদা ময়দা ১ কেজি",
        "Foam Fina Wheat Flour 10kg" to "ফোম ফিনা গম ময়দা ১০ কেজি",
        "Foam Fina White Flour 10kg" to "ফোম ফিনা সাদা ময়দা ১০ কেজি",
        
        // Milk and Dairy
        "Wadi Fatima Milk 170g" to "ওয়াদি ফাতিমা দুধ ১৭০ গ্রাম",
        "Nido 1800g" to "নিডো ১৮০০ গ্রাম",
        "Nido 900g" to "নিডো ৯০০ গ্রাম",
        "Abu Qaws Milk 160ml" to "আবু কাওস দুধ ১৬০ মিলি",
        "Luna Powdered Milk 1800g" to "লুনা গুঁড়ো দুধ ১৮০০ গ্রাম",
        "Luna Powdered Milk 900g" to "লুনা গুঁড়ো দুধ ৯০০ গ্রাম",
        "Nadec Milk 1L" to "নাদেক দুধ ১ লিটার",
        "Nadec Cheese 500g" to "নাদেক পনির ৫০০ গ্রাম",
        "Roya Triangle Cheese 100g" to "রয়া ত্রিভুজ পনির ১০০ গ্রাম",
        
        // Rice
        "Al Rasheed Indian Basmati Rice 10kg" to "আল রশিদ ভারতীয় বাসমতি চাল ১০ কেজি",
        "Aroma Sella Mazza Rice 10kg" to "অ্যারোমা সেলা মাজা চাল ১০ কেজি",
        "Al Shaalan Mazza Rice 10kg" to "আল শালান মাজা চাল ১০ কেজি",
        "Al Aila Rice 10kg" to "আল আইলা চাল ১০ কেজি",
        "Bab Al Hind Rice 5kg" to "বাব আল হিন্দ চাল ৫ কেজি",
        
        // Chicken
        "Supreme Chicken 1000g" to "সুপ্রিম মুরগি ১০০০ গ্রাম",
        "Al Watania Chicken 1000g" to "আল ওয়াতানিয়া মুরগি ১০০০ গ্রাম",
        
        // Tahini
        "Al Thahabi Premium Tahini 250g" to "আল থাহাবি প্রিমিয়াম তাহিনি ২৫০ গ্রাম",
        
        // Canned Food
        "Isnad Chicken Stock Cubes 24 Pack" to "ইসনাদ মুরগির স্টক কিউব ২৪ প্যাক",
        "Coopoliva Black Olives 75g" to "কুপোলিভা কালো জলপাই ৭৫ গ্রাম",
        "Coopoliva Olives 150g" to "কুপোলিভা জলপাই ১৫০ গ্রাম",
        "Aloha Tuna 158g" to "আলোহা টুনা ১৫৮ গ্রাম",
        
        // Personal Care
        "Sunsilk Shampoo 400ml" to "সানসিল্ক শ্যাম্পু ৪০০ মিলি",
        "Dove Daily Care Shampoo 400ml" to "ডাভ ডেইলি কেয়ার শ্যাম্পু ৪০০ মিলি",
        
        // Snacks
        "Al Batal Small Popcorn" to "আল বাতাল ছোট পপকর্ন",
        
        // Beverages
        "7UP 240ml" to "সেভেন আপ ২৪০ মিলি",
        "Citrus Drink 240ml" to "সাইট্রাস পানীয় ২৪০ মিলি",
        "Captain Orange Juice 250ml" to "ক্যাপ্টেন কমলা জুস ২৫০ মিলি"
    )
    
    // Map of Bangla product names to their English translations (reverse mapping)
    private val banglaToenglish = englishToBangla.entries.associate { (k, v) -> v to k }
    
    /**
     * Get the Bangla translation for an English product name
     * @param englishName The English product name
     * @return The Bangla translation or null if no translation is found
     */
    fun getBanglaName(englishName: String): String? {
        return englishToBangla[englishName]
    }
    
    /**
     * Get the English translation for a Bangla product name
     * @param banglaName The Bangla product name
     * @return The English translation or null if no translation is found
     */
    fun getEnglishName(banglaName: String): String? {
        return banglaToenglish[banglaName]
    }
    
    /**
     * Check if a translation exists for the given English product name
     * @param englishName The English product name to check
     * @return True if a translation exists, false otherwise
     */
    fun hasEnglishToBanglaTranslation(englishName: String): Boolean {
        return englishToBangla.containsKey(englishName)
    }
    
    /**
     * Check if a translation exists for the given Bangla product name
     * @param banglaName The Bangla product name to check
     * @return True if a translation exists, false otherwise
     */
    fun hasBanglaToEnglishTranslation(banglaName: String): Boolean {
        return banglaToenglish.containsKey(banglaName)
    }
    
    /**
     * Find the best matching product name for fuzzy matching
     * @param text The text to match
     * @return The best matching product name or null if no match is found
     */
    fun findBestMatchForEnglish(text: String): Pair<String, String>? {
        val lowerText = text.toLowerCase()
        var bestMatch: Pair<String, Int>? = null
        var matchedKey: String? = null
        
        // Check each product name
        for ((englishName, _) in englishToBangla) {
            val lowerProductName = englishName.toLowerCase()
            
            // Calculate similarity (simple word overlap for now)
            val textWords = lowerText.split(" ")
            val productWords = lowerProductName.split(" ")
            
            var matchCount = 0
            for (textWord in textWords) {
                if (textWord.length < 3) continue // Skip very short words
                
                for (productWord in productWords) {
                    if (productWord.length < 3) continue // Skip very short words
                    
                    if (textWord == productWord || 
                        textWord.contains(productWord) || 
                        productWord.contains(textWord)) {
                        matchCount++
                        break
                    }
                }
            }
            
            // Update best match if this is better
            if (matchCount > 0 && (bestMatch == null || matchCount > bestMatch.second)) {
                bestMatch = Pair(englishName, matchCount)
                matchedKey = englishName
            }
        }
        
        // Return the best match and its translation
        return if (matchedKey != null) {
            Pair(matchedKey, englishToBangla[matchedKey]!!)
        } else {
            null
        }
    }
    
    /**
     * Find the best matching Bangla product name for fuzzy matching
     * @param text The Bangla text to match
     * @return The best matching product name pair (Bangla, English) or null if no match is found
     */
    fun findBestMatchForBangla(text: String): Pair<String, String>? {
        var bestMatch: Pair<String, Int>? = null
        var matchedKey: String? = null
        
        // Check each Bangla product name
        for ((banglaName, _) in banglaToenglish) {
            // Calculate similarity (character overlap for Bangla)
            var matchCount = 0
            var i = 0
            while (i < text.length && i < banglaName.length) {
                if (text[i] == banglaName[i]) {
                    matchCount++
                }
                i++
            }
            
            // Add bonus for length similarity
            val lengthDiff = Math.abs(text.length - banglaName.length)
            val lengthSimilarity = Math.max(0, 10 - lengthDiff) // 0-10 bonus
            matchCount += lengthSimilarity
            
            // Update best match if this is better
            if (matchCount > 0 && (bestMatch == null || matchCount > bestMatch.second)) {
                bestMatch = Pair(banglaName, matchCount)
                matchedKey = banglaName
            }
        }
        
        // Return the best match and its translation
        return if (matchedKey != null) {
            Pair(matchedKey, banglaToenglish[matchedKey]!!)
        } else {
            null
        }
    }
}

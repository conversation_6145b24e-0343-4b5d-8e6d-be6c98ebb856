package rt.tt.temp.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import rt.tt.temp.data.AppDatabase
import rt.tt.temp.data.Product
import kotlinx.coroutines.launch
import android.util.Log

@Composable
fun ThresholdScreen(
    database: AppDatabase,
    modifier: Modifier = Modifier
) {
    val scope = rememberCoroutineScope()
    var products by remember { mutableStateOf(listOf<Product>()) }
    var searchQuery by remember { mutableStateOf("") }
    var expandedCardId by remember { mutableStateOf<Int?>(null) }

    LaunchedEffect(Unit) {
        products = database.productDao().getAllProducts()
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        OutlinedTextField(
            value = searchQuery,
            onValueChange = { searchQuery = it },
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            placeholder = { Text("Search products...") },
            singleLine = true
        )

        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            val filteredProducts = if (searchQuery.isBlank()) {
                products
            } else {
                products.filter { product ->
                    product.productName.contains(searchQuery, ignoreCase = true) ||
                    product.productType.contains(searchQuery, ignoreCase = true) ||
                    product.barcode.contains(searchQuery, ignoreCase = true)
                }
            }

            items(
                items = filteredProducts,
                key = { it.id }
            ) { product ->
                ProductThresholdCard(
                    product = product,
                    isExpanded = expandedCardId == product.id,
                    onExpandClick = { 
                        expandedCardId = if (expandedCardId == product.id) null else product.id
                    },
                    onSaveThreshold = { threshold ->
                        scope.launch {
                            try {
                                database.productDao().updateThreshold(product.id, threshold)
                                // Refresh products list after update
                                products = database.productDao().getAllProducts()
                            } catch (e: Exception) {
                                Log.e("ThresholdScreen", "Error updating threshold", e)
                            }
                        }
                    }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ProductThresholdCard(
    product: Product,
    isExpanded: Boolean,
    onExpandClick: () -> Unit,
    onSaveThreshold: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    var threshold by remember(product.id) { 
        mutableStateOf(product.maxQuantityThreshold?.toString() ?: "") 
    }
    var showError by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }

    ElevatedCard(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        onClick = onExpandClick
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = product.productName,
                        style = MaterialTheme.typography.titleMedium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    Text(
                        text = product.productType,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "Barcode: ${product.barcode}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                IconButton(onClick = onExpandClick) {
                    Icon(
                        if (isExpanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                        contentDescription = if (isExpanded) "Show less" else "Show more"
                    )
                }
            }

            if (isExpanded) {
                Spacer(modifier = Modifier.height(16.dp))
                Divider()
                Spacer(modifier = Modifier.height(16.dp))

                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "Current Quantity: ${product.currentQuantity}",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "Initial Quantity: ${product.initialQuantity}",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "Threshold Amount: ${product.maxQuantityThreshold ?: "Not set"}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = if (product.maxQuantityThreshold != null && 
                                 product.currentQuantity <= product.maxQuantityThreshold) {
                            MaterialTheme.colorScheme.error
                        } else {
                            MaterialTheme.colorScheme.onSurface
                        }
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        IconButton(
                            onClick = {
                                val current = threshold.toIntOrNull() ?: 0
                                if (current > 0) {
                                    threshold = (current - 1).toString()
                                }
                            }
                        ) {
                            Icon(Icons.Default.Remove, "Decrease threshold")
                        }

                        OutlinedTextField(
                            value = threshold,
                            onValueChange = { newValue ->
                                if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                                    threshold = newValue
                                    showError = false
                                }
                            },
                            modifier = Modifier.weight(1f),
                            label = { Text("Max Quantity Threshold") },
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                            isError = showError,
                            supportingText = if (showError) {
                                { Text(errorMessage) }
                            } else null,
                            singleLine = true
                        )

                        IconButton(
                            onClick = {
                                val current = threshold.toIntOrNull() ?: 0
                                threshold = (current + 1).toString()
                            }
                        ) {
                            Icon(Icons.Default.Add, "Increase threshold")
                        }
                    }

                    if (showError) {
                        Text(
                            text = errorMessage,
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodySmall
                        )
                    }

                    Button(
                        onClick = {
                            val thresholdValue = threshold.toIntOrNull()
                            when {
                                thresholdValue == null -> {
                                    showError = true
                                    errorMessage = "Please enter a valid number"
                                }
                                thresholdValue < 0 -> {
                                    showError = true
                                    errorMessage = "Threshold cannot be negative"
                                }
                                else -> {
                                    onSaveThreshold(thresholdValue)
                                    showError = false
                                }
                            }
                        },
                        modifier = Modifier.align(Alignment.End)
                    ) {
                        Text("Save Threshold")
                    }
                }
            }
        }
    }
}
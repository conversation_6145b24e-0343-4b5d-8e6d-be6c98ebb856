package rt.tt.temp.notifications

import android.os.Build
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import rt.tt.temp.R
import rt.tt.temp.MainActivity
import rt.tt.temp.data.Product
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

// Helper class for returning multiple values
data class NotificationData(
    val title: String,
    val content: String,
    val priority: Int,
    val category: String,
    val color: Int,
    val icon: Int
)

class NotificationService(private val context: Context) {
    companion object {
        // Notification channels
        const val CHANNEL_ID_EXPIRY = "expiry_notifications"
        const val CHANNEL_ID_STOCK = "stock_notifications"
        const val CHANNEL_ID_SYSTEM = "system_notifications"

        // Notification groups
        const val EXPIRY_GROUP_ID = "expiry_notifications_group"
        const val QUANTITY_GROUP_ID = "quantity_notifications_group"
        const val BACKUP_GROUP_ID = "backup_notifications_group"

        // Notification IDs
        const val EXPIRY_NOTIFICATION_ID = 1000
        const val QUANTITY_NOTIFICATION_ID = 2000
        const val BACKUP_NOTIFICATION_ID = 3000
        const val EXPIRY_SUMMARY_ID = 1100
        const val QUANTITY_SUMMARY_ID = 2100
        const val BACKUP_SUMMARY_ID = 3100

        // Notification categories
        const val CATEGORY_CRITICAL = "critical" // < 30 days
        const val CATEGORY_WARNING = "warning"   // 30-90 days
        const val CATEGORY_INFO = "info"         // > 90 days

        // Backup status codes
        const val BACKUP_STATUS_STARTED = 0
        const val BACKUP_STATUS_SUCCESS = 1
        const val BACKUP_STATUS_FAILED = 2
    }

    private val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

    init {
        createNotificationChannel()
    }

    private fun getMainActivityPendingIntent(): PendingIntent {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        return PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    private fun showGroupSummary(groupId: String, summaryId: Int, summaryText: String, channelId: String) {
        val summary = NotificationCompat.Builder(context, channelId)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(summaryText)
            .setGroup(groupId)
            .setGroupSummary(true)
            .setAutoCancel(true)
            .setGroupAlertBehavior(NotificationCompat.GROUP_ALERT_CHILDREN)
            .build()

        notificationManager.notify(summaryId, summary)
    }

    fun showDemoNotifications() {
        // This function will remain for demonstration purposes
        val mainPendingIntent = getMainActivityPendingIntent()

        // Demo expiry notification
        val expiryNotification = NotificationCompat.Builder(context, CHANNEL_ID_EXPIRY)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle("Product Expiring Soon")
            .setContentText("Demo: 2 products will expire in 3 months")
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .setGroup(EXPIRY_GROUP_ID)
            .setAutoCancel(true)
            .setContentIntent(mainPendingIntent)
            .setVibrate(longArrayOf(1000, 500, 1000))
            .setColor(ContextCompat.getColor(context, android.R.color.holo_red_light))
            .build()

        // Demo quantity notification
        val quantityNotification = NotificationCompat.Builder(context, CHANNEL_ID_STOCK)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle("Low Stock Alert")
            .setContentText("Demo: 3 products are below threshold")
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setCategory(NotificationCompat.CATEGORY_STATUS)
            .setGroup(QUANTITY_GROUP_ID)
            .setAutoCancel(true)
            .setContentIntent(mainPendingIntent)
            .setVibrate(longArrayOf(500, 500, 500))
            .setColor(ContextCompat.getColor(context, android.R.color.holo_orange_dark))
            .build()

        notificationManager.notify(EXPIRY_NOTIFICATION_ID, expiryNotification)
        notificationManager.notify(QUANTITY_NOTIFICATION_ID, quantityNotification)

        // Show group summaries if on Android 7.0+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            showGroupSummary(EXPIRY_GROUP_ID, EXPIRY_SUMMARY_ID, "Product Expiry Alerts", CHANNEL_ID_EXPIRY)
            showGroupSummary(QUANTITY_GROUP_ID, QUANTITY_SUMMARY_ID, "Stock Alerts", CHANNEL_ID_STOCK)
        }
    }

    /**
     * Show notifications for products that are expiring soon
     */
    fun showExpiryNotifications(products: List<Product>) {
        val mainPendingIntent = getMainActivityPendingIntent()
        val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())

        // Cancel previous expiry notifications
        notificationManager.cancel(EXPIRY_SUMMARY_ID)

        // If no products need notification, cancel all related notifications and return
        if (products.isEmpty()) {
            // Cancel individual notifications (using a range that covers all possible IDs)
            for (i in EXPIRY_NOTIFICATION_ID until EXPIRY_NOTIFICATION_ID + 100) {
                notificationManager.cancel(i)
            }
            notificationManager.cancel(EXPIRY_SUMMARY_ID)
            return
        }

        // Group products by expiry category
        val criticalProducts = products.filter {
            val daysUntilExpiry = ((it.expireDate - System.currentTimeMillis()) / (1000 * 60 * 60 * 24)).toInt()
            daysUntilExpiry <= 30 && daysUntilExpiry >= 0
        }

        val warningProducts = products.filter {
            val daysUntilExpiry = ((it.expireDate - System.currentTimeMillis()) / (1000 * 60 * 60 * 24)).toInt()
            daysUntilExpiry in 31..90
        }

        val expiredProducts = products.filter {
            ((it.expireDate - System.currentTimeMillis()) / (1000 * 60 * 60 * 24)).toInt() < 0
        }

        // If there are 5 or fewer products, show individual notifications
        if (products.size <= 5) {
            products.forEachIndexed { index, product ->
                val daysUntilExpiry = ((product.expireDate - System.currentTimeMillis()) / (1000 * 60 * 60 * 24)).toInt()

                // Determine notification style based on days until expiry
                val (priority, category, color) = when {
                    daysUntilExpiry < 0 -> Triple(
                        NotificationCompat.PRIORITY_HIGH,
                        NotificationCompat.CATEGORY_ERROR,
                        ContextCompat.getColor(context, android.R.color.holo_red_dark)
                    )
                    daysUntilExpiry <= 30 -> Triple(
                        NotificationCompat.PRIORITY_HIGH,
                        NotificationCompat.CATEGORY_REMINDER,
                        ContextCompat.getColor(context, android.R.color.holo_red_light)
                    )
                    daysUntilExpiry <= 90 -> Triple(
                        NotificationCompat.PRIORITY_DEFAULT,
                        NotificationCompat.CATEGORY_REMINDER,
                        ContextCompat.getColor(context, android.R.color.holo_orange_light)
                    )
                    else -> Triple(
                        NotificationCompat.PRIORITY_LOW,
                        NotificationCompat.CATEGORY_STATUS,
                        ContextCompat.getColor(context, android.R.color.holo_green_light)
                    )
                }

                // Create content text based on days until expiry
                val contentText = when {
                    daysUntilExpiry < 0 -> "Expired on ${dateFormat.format(Date(product.expireDate))}"
                    daysUntilExpiry == 0 -> "Expires today!"
                    daysUntilExpiry == 1 -> "Expires tomorrow"
                    else -> "Expires in ${daysUntilExpiry} days (${dateFormat.format(Date(product.expireDate))})"
                }

                // Create notification style with more details
                val style = NotificationCompat.BigTextStyle()
                    .bigText(contentText)
                    .setSummaryText("${product.productType} - ${product.barcode}")

                val notification = NotificationCompat.Builder(context, CHANNEL_ID_EXPIRY)
                    .setSmallIcon(R.drawable.ic_notification)
                    .setContentTitle(product.productName)
                    .setContentText(contentText)
                    .setStyle(style)
                    .setPriority(priority)
                    .setCategory(category)
                    .setColor(color)
                    .setGroup(EXPIRY_GROUP_ID)
                    .setAutoCancel(true)
                    .setContentIntent(mainPendingIntent)
                    .build()

                notificationManager.notify(EXPIRY_NOTIFICATION_ID + index, notification)
            }
        } else {
            // For larger numbers of products, create a summary notification with expandable details

            // Create notification style with details for each category
            val style = NotificationCompat.InboxStyle()
                .setBigContentTitle("Product Expiry Summary")

            if (expiredProducts.isNotEmpty()) {
                style.addLine("⚠️ ${expiredProducts.size} products have expired")
            }

            if (criticalProducts.isNotEmpty()) {
                style.addLine("🔴 ${criticalProducts.size} products expire within 30 days")
            }

            if (warningProducts.isNotEmpty()) {
                style.addLine("🟠 ${warningProducts.size} products expire within 90 days")
            }

            style.setSummaryText("${products.size} products need attention")

            // Create the summary notification
            val summary = NotificationCompat.Builder(context, CHANNEL_ID_EXPIRY)
                .setSmallIcon(R.drawable.ic_notification)
                .setContentTitle("Product Expiry Alert")
                .setContentText("${products.size} products are expiring soon")
                .setStyle(style)
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setCategory(NotificationCompat.CATEGORY_REMINDER)
                .setGroup(EXPIRY_GROUP_ID)
                .setGroupSummary(true)
                .setAutoCancel(true)
                .setContentIntent(mainPendingIntent)
                .setColor(ContextCompat.getColor(context, android.R.color.holo_red_light))
                .build()

            notificationManager.notify(EXPIRY_SUMMARY_ID, summary)

            // Also create individual notifications for critical products
            criticalProducts.take(3).forEachIndexed { index, product ->
                val daysUntilExpiry = ((product.expireDate - System.currentTimeMillis()) / (1000 * 60 * 60 * 24)).toInt()

                val contentText = when {
                    daysUntilExpiry == 0 -> "Expires today!"
                    daysUntilExpiry == 1 -> "Expires tomorrow"
                    else -> "Expires in ${daysUntilExpiry} days"
                }

                val notification = NotificationCompat.Builder(context, CHANNEL_ID_EXPIRY)
                    .setSmallIcon(R.drawable.ic_notification)
                    .setContentTitle(product.productName)
                    .setContentText(contentText)
                    .setPriority(NotificationCompat.PRIORITY_HIGH)
                    .setCategory(NotificationCompat.CATEGORY_REMINDER)
                    .setGroup(EXPIRY_GROUP_ID)
                    .setAutoCancel(true)
                    .setContentIntent(mainPendingIntent)
                    .setColor(ContextCompat.getColor(context, android.R.color.holo_red_light))
                    .build()

                notificationManager.notify(EXPIRY_NOTIFICATION_ID + index, notification)
            }
        }
    }

    /**
     * Show notifications for products with low stock levels
     */
    fun showQuantityNotifications(products: List<Product>) {
        val mainPendingIntent = getMainActivityPendingIntent()

        // Cancel previous stock notifications
        notificationManager.cancel(QUANTITY_SUMMARY_ID)

        // If no products need notification, cancel all related notifications and return
        if (products.isEmpty()) {
            // Cancel individual notifications (using a range that covers all possible IDs)
            for (i in QUANTITY_NOTIFICATION_ID until QUANTITY_NOTIFICATION_ID + 100) {
                notificationManager.cancel(i)
            }
            notificationManager.cancel(QUANTITY_SUMMARY_ID)
            return
        }

        // Group products by stock level severity
        val criticalStock = products.filter {
            it.currentQuantity <= (it.initialQuantity * 0.1) // Less than 10% of initial quantity
        }

        val lowStock = products.filter {
            val percentage = it.currentQuantity.toFloat() / it.initialQuantity.toFloat()
            percentage > 0.1f && percentage <= 0.25f // Between 10% and 25% of initial quantity
        }

        val moderateStock = products.filter {
            val percentage = it.currentQuantity.toFloat() / it.initialQuantity.toFloat()
            percentage > 0.25f && percentage <= 0.5f // Between 25% and 50% of initial quantity
        }

        // If there are 5 or fewer products, show individual notifications
        if (products.size <= 5) {
            products.forEachIndexed { index, product ->
                val stockPercentage = (product.currentQuantity.toFloat() / product.initialQuantity.toFloat()) * 100

                // Determine notification style based on stock level
                val (priority, category, color) = when {
                    stockPercentage <= 10 -> Triple(
                        NotificationCompat.PRIORITY_HIGH,
                        NotificationCompat.CATEGORY_ERROR,
                        ContextCompat.getColor(context, android.R.color.holo_red_dark)
                    )
                    stockPercentage <= 25 -> Triple(
                        NotificationCompat.PRIORITY_DEFAULT,
                        NotificationCompat.CATEGORY_STATUS,
                        ContextCompat.getColor(context, android.R.color.holo_orange_dark)
                    )
                    else -> Triple(
                        NotificationCompat.PRIORITY_LOW,
                        NotificationCompat.CATEGORY_STATUS,
                        ContextCompat.getColor(context, android.R.color.holo_orange_light)
                    )
                }

                // Create content text based on stock level
                val contentText = when {
                    stockPercentage <= 10 -> "Critical stock level: ${product.currentQuantity} units (${stockPercentage.toInt()}%)"
                    stockPercentage <= 25 -> "Low stock level: ${product.currentQuantity} units (${stockPercentage.toInt()}%)"
                    else -> "Moderate stock level: ${product.currentQuantity} units (${stockPercentage.toInt()}%)"
                }

                // Create notification style with more details
                val style = NotificationCompat.BigTextStyle()
                    .bigText(contentText)
                    .setSummaryText("Initial quantity: ${product.initialQuantity} units")

                val notification = NotificationCompat.Builder(context, CHANNEL_ID_STOCK)
                    .setSmallIcon(R.drawable.ic_notification)
                    .setContentTitle(product.productName)
                    .setContentText(contentText)
                    .setStyle(style)
                    .setPriority(priority)
                    .setCategory(category)
                    .setColor(color)
                    .setGroup(QUANTITY_GROUP_ID)
                    .setAutoCancel(true)
                    .setContentIntent(mainPendingIntent)
                    .build()

                notificationManager.notify(QUANTITY_NOTIFICATION_ID + index, notification)
            }
        } else {
            // For larger numbers of products, create a summary notification with expandable details

            // Create notification style with details for each category
            val style = NotificationCompat.InboxStyle()
                .setBigContentTitle("Stock Level Summary")

            if (criticalStock.isNotEmpty()) {
                style.addLine("🔴 ${criticalStock.size} products at critical stock level (<10%)")
            }

            if (lowStock.isNotEmpty()) {
                style.addLine("🟠 ${lowStock.size} products at low stock level (<25%)")
            }

            if (moderateStock.isNotEmpty()) {
                style.addLine("🟡 ${moderateStock.size} products at moderate stock level (<50%)")
            }

            style.setSummaryText("${products.size} products need restocking")

            // Create the summary notification
            val summary = NotificationCompat.Builder(context, CHANNEL_ID_STOCK)
                .setSmallIcon(R.drawable.ic_notification)
                .setContentTitle("Stock Level Alert")
                .setContentText("${products.size} products need restocking")
                .setStyle(style)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setCategory(NotificationCompat.CATEGORY_STATUS)
                .setGroup(QUANTITY_GROUP_ID)
                .setGroupSummary(true)
                .setAutoCancel(true)
                .setContentIntent(mainPendingIntent)
                .setColor(ContextCompat.getColor(context, android.R.color.holo_orange_dark))
                .build()

            notificationManager.notify(QUANTITY_SUMMARY_ID, summary)

            // Also create individual notifications for critical stock products
            criticalStock.take(3).forEachIndexed { index, product ->
                val stockPercentage = (product.currentQuantity.toFloat() / product.initialQuantity.toFloat()) * 100

                val contentText = "Critical stock level: ${product.currentQuantity} units (${stockPercentage.toInt()}%)"

                val notification = NotificationCompat.Builder(context, CHANNEL_ID_STOCK)
                    .setSmallIcon(R.drawable.ic_notification)
                    .setContentTitle(product.productName)
                    .setContentText(contentText)
                    .setPriority(NotificationCompat.PRIORITY_HIGH)
                    .setCategory(NotificationCompat.CATEGORY_STATUS)
                    .setGroup(QUANTITY_GROUP_ID)
                    .setAutoCancel(true)
                    .setContentIntent(mainPendingIntent)
                    .setColor(ContextCompat.getColor(context, android.R.color.holo_red_dark))
                    .build()

                notificationManager.notify(QUANTITY_NOTIFICATION_ID + index, notification)
            }
        }
    }

    /**
     * Show backup status notifications
     * @param status The backup status code (BACKUP_STATUS_STARTED, BACKUP_STATUS_SUCCESS, BACKUP_STATUS_FAILED)
     * @param message Optional message with additional details
     * @param file Optional file that was created during backup
     */
    fun showBackupNotification(status: Int, message: String? = null, file: java.io.File? = null) {
        val mainPendingIntent = getMainActivityPendingIntent()

        // Determine notification content based on status
        val notificationData = when (status) {
            BACKUP_STATUS_STARTED -> {
                val title = "Backup in Progress"
                val content = message ?: "Creating backup of your inventory data..."
                val priority = NotificationCompat.PRIORITY_LOW
                val category = NotificationCompat.CATEGORY_PROGRESS
                val color = ContextCompat.getColor(context, android.R.color.holo_blue_light)
                val icon = R.drawable.ic_notification // Replace with backup icon if available

                // Create a progress notification
                val notification = NotificationCompat.Builder(context, CHANNEL_ID_SYSTEM)
                    .setSmallIcon(icon)
                    .setContentTitle(title)
                    .setContentText(content)
                    .setPriority(priority)
                    .setCategory(category)
                    .setColor(color)
                    .setOngoing(true) // Can't be dismissed by user
                    .setProgress(0, 0, true) // Indeterminate progress
                    .build()

                notificationManager.notify(BACKUP_NOTIFICATION_ID, notification)

                return // Return early as we don't need the rest of the method
            }
            BACKUP_STATUS_SUCCESS -> {
                val title = "Backup Completed"
                val content = message ?: "Your inventory data has been successfully backed up"
                val priority = NotificationCompat.PRIORITY_DEFAULT
                val category = NotificationCompat.CATEGORY_STATUS
                val color = ContextCompat.getColor(context, android.R.color.holo_green_light)
                val icon = R.drawable.ic_notification // Replace with success icon if available

                NotificationData(title, content, priority, category, color, icon)
            }
            BACKUP_STATUS_FAILED -> {
                val title = "Backup Failed"
                val content = message ?: "There was a problem creating your backup"
                val priority = NotificationCompat.PRIORITY_HIGH
                val category = NotificationCompat.CATEGORY_ERROR
                val color = ContextCompat.getColor(context, android.R.color.holo_red_light)
                val icon = R.drawable.ic_notification // Replace with error icon if available

                NotificationData(title, content, priority, category, color, icon)
            }
            else -> {
                val title = "Backup Status"
                val content = message ?: "Backup status update"
                val priority = NotificationCompat.PRIORITY_DEFAULT
                val category = NotificationCompat.CATEGORY_STATUS
                val color = ContextCompat.getColor(context, android.R.color.darker_gray)
                val icon = R.drawable.ic_notification

                NotificationData(title, content, priority, category, color, icon)
            }
        }

        // Create notification builder
        val builder = NotificationCompat.Builder(context, CHANNEL_ID_SYSTEM)
            .setSmallIcon(notificationData.icon)
            .setContentTitle(notificationData.title)
            .setContentText(notificationData.content)
            .setPriority(notificationData.priority)
            .setCategory(notificationData.category)
            .setColor(notificationData.color)
            .setAutoCancel(true)
            .setContentIntent(mainPendingIntent)

        // Add action to open file if available
        if (file != null && status == BACKUP_STATUS_SUCCESS) {
            // Create a style with more details
            val style = NotificationCompat.BigTextStyle()
                .bigText(notificationData.content)
                .setSummaryText("Backup file: ${file.name}")

            builder.setStyle(style)

            // Add action to open the file
            val openFileIntent = Intent(Intent.ACTION_VIEW).apply {
                val uri = androidx.core.content.FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.provider",
                    file
                )
                setDataAndType(uri, "application/json")
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }

            val openPendingIntent = PendingIntent.getActivity(
                context,
                0,
                openFileIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            builder.addAction(
                R.drawable.ic_notification, // Replace with open icon if available
                "Open Backup",
                openPendingIntent
            )
        }

        // Show the notification
        notificationManager.notify(BACKUP_NOTIFICATION_ID, builder.build())
    }

    /**
     * Set up notification channels
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Expiry notification channel - High importance
            val expiryChannel = NotificationChannel(
                CHANNEL_ID_EXPIRY,
                "Expiry Alerts",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications about products that are expiring soon"
                enableVibration(true)
                vibrationPattern = longArrayOf(1000, 500, 1000)
                setBypassDnd(true) // Bypass Do Not Disturb mode
                setShowBadge(true)
                enableLights(true)
                lightColor = Color.RED
            }

            // Stock notification channel - Default importance
            val stockChannel = NotificationChannel(
                CHANNEL_ID_STOCK,
                "Stock Alerts",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Notifications about low stock levels"
                enableVibration(true)
                vibrationPattern = longArrayOf(500, 500, 500)
                setShowBadge(true)
                enableLights(true)
                lightColor = Color.YELLOW
            }

            // System notification channel - Low importance
            val systemChannel = NotificationChannel(
                CHANNEL_ID_SYSTEM,
                "System Notifications",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "System notifications like backup status"
                setShowBadge(false)
            }

            // Create all channels
            notificationManager.createNotificationChannels(listOf(expiryChannel, stockChannel, systemChannel))
        }
    }
}
package rt.tt.temp.utils

import android.os.Environment
import java.io.File

object DirectoryUtils {
    const val APP_FOLDER_NAME = "EXP TRACK"
    const val EXPORT_FOLDER_NAME = "Export"
    const val PDF_FOLDER_NAME = "PDF"
    const val CSV_FOLDER_NAME = "CSV"
    const val EXCEL_FOLDER_NAME = "Excel"
    const val BACKUP_FOLDER_NAME = "backup"

    fun getPublicAppDirectory(): File {
        val downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
        val appFolder = File(downloadsDir, APP_FOLDER_NAME)
        
        if (!appFolder.exists()) {
            appFolder.mkdirs()
        }
        
        return appFolder
    }

    fun getPublicExportDirectory(): File {
        val appFolder = getPublicAppDirectory()
        val exportFolder = File(appFolder, EXPORT_FOLDER_NAME)
        
        if (!exportFolder.exists()) {
            exportFolder.mkdirs()
        }
        
        return exportFolder
    }

    fun getPdfExportDirectory(): File {
        val exportFolder = getPublicExportDirectory()
        val pdfFolder = File(exportFolder, PDF_FOLDER_NAME)
        
        if (!pdfFolder.exists()) {
            pdfFolder.mkdirs()
        }
        
        return pdfFolder
    }

    fun getCsvExportDirectory(): File {
        val exportFolder = getPublicExportDirectory()
        val csvFolder = File(exportFolder, CSV_FOLDER_NAME)
        
        if (!csvFolder.exists()) {
            csvFolder.mkdirs()
        }
        
        return csvFolder
    }

    fun getExcelExportDirectory(): File {
        val exportFolder = getPublicExportDirectory()
        val excelFolder = File(exportFolder, EXCEL_FOLDER_NAME)
        
        if (!excelFolder.exists()) {
            excelFolder.mkdirs()
        }
        
        return excelFolder
    }

    fun getJsonExportDirectory(): File {
        val exportFolder = getPublicExportDirectory()
        val jsonFolder = File(exportFolder, "JSON")

        if (!jsonFolder.exists()) {
            jsonFolder.mkdirs()
        }

        return jsonFolder
    }
}

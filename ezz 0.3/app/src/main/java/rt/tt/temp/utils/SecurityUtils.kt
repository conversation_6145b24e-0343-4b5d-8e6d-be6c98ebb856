package rt.tt.temp.utils

import java.security.SecureRandom
import java.security.spec.KeySpec
import java.util.*
import javax.crypto.SecretKeyFactory
import javax.crypto.spec.PBEKeySpec
import kotlin.io.encoding.Base64
import kotlin.io.encoding.ExperimentalEncodingApi

object SecurityUtils {
    private const val ITERATIONS = 65536
    private const val KEY_LENGTH = 256

    @OptIn(ExperimentalEncodingApi::class)
    fun generateSalt(): String {
        val salt = ByteArray(16)
        SecureRandom().nextBytes(salt)
        return Base64.encode(salt)
    }

    @OptIn(ExperimentalEncodingApi::class)
    fun hashPassword(password: String, salt: String): String {
        val saltBytes = Base64.decode(salt)
        val spec: KeySpec = PBEKeySpec(
            password.toCharArray(),
            saltBytes,
            ITERATIONS,
            KEY_LENGTH
        )
        val factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256")
        val hash = factory.generateSecret(spec).encoded
        return Base64.encode(hash)
    }

    fun generateResetToken(): String {
        return UUID.randomUUID().toString()
    }

    fun isPasswordValid(password: String): Boolean {
        return password.length >= 6 && 
               password.any { it.isDigit() } && 
               password.any { it.isLetter() }
    }
}
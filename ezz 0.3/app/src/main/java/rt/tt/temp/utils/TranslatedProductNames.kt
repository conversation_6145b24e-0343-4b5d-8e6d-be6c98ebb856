package rt.tt.temp.utils

/**
 * Class containing translated product names from Arabic to English
 * This serves as a local translation database for product names
 */
object TranslatedProductNames {
    // Map of Arabic product names to their English translations
    private val translations = mapOf(
        // Spices
        "بهارات كركم" to "Turmeric Spices",
        "بوكس بهارات فلفل حار" to "Hot Pepper Spices Box",
        "بوكس بهارات مشكل" to "Mixed Spices Box",
        "قرنفل أندونسي 180 جرام" to "Indonesian Cloves 180g",
        "هيل فرحة 250 جرام" to "Farha Cardamom 250g",
        
        // Cleaning Products
        "كلوركس كبير" to "Large Clorox",
        "بف باف بودرة" to "Pif Paf Powder",
        "تايد عادى 5 كيلو" to "Regular Tide 5kg",
        "تايد أتوماتيك 5كيلو" to "Automatic Tide 5kg",
        
        // Oils
        "زيت شمس 1.5 لتر" to "Shams Oil 1.5L",
        "زيت زيتون الجوف 250 مل" to "Al Jouf Olive Oil 250ml",
        
        // Tea and Coffee
        "شاي الكبوس يمني علاقي 100" to "Al Kabous Yemeni Alaqi Tea 100",
        "شاي ليبتون علاقي 100- 36" to "Lipton Alaqi Tea 100-36",
        "شاي 999" to "999 Tea",
        "شاي الكبوس 227 جرام تلقيمة" to "Al Kabous Tea 227g",
        "شاى ليبتون العلامة الصفراء 36×100" to "Lipton Yellow Label Tea 36×100",
        "نسكافيه بودرة - 190جرام" to "Nescafe Powder - 190g",
        "قهوة رواق 500جم" to "Rawaq Coffee 500g",
        
        // Sugar
        "سكر الاسرة ناعم 5 كجم" to "Al Osra Fine Sugar 5kg",
        "سكر الاسرة خشن 5 كجم" to "Al Osra Coarse Sugar 5kg",
        "سكر الاسراء خشن 5 كجم" to "Al Israa Coarse Sugar 5kg",
        
        // Noodles and Pasta
        "اندومي كوب دجاج 60 جرام" to "Indomie Chicken Cup 60g",
        "كيس اندومي خضار 5 حبة" to "Indomie Vegetable Pack 5 pieces",
        "مكرونة بريفيتو إسباكتي 450 جرام" to "Perfetto Spaghetti 450g",
        
        // Flour
        "دقيق فوم ابيض 1حبه 1كيلو" to "Foam White Flour 1kg",
        "دقيق فوم فينا بر 10 ك" to "Foam Fina Wheat Flour 10kg",
        "دقيق فوم فينا ابيض 10 ك" to "Foam Fina White Flour 10kg",
        
        // Milk and Dairy
        "حليب وادي فاطمة 170 جرام" to "Wadi Fatima Milk 170g",
        "نيدو 1800 جرام" to "Nido 1800g",
        "نيدو 900 جرام" to "Nido 900g",
        "حليب ابو قوس 160مل" to "Abu Qaws Milk 160ml",
        "حليب لونا مجفف 1800 جرام" to "Luna Powdered Milk 1800g",
        "حليب لونا مجفف 900 جرام" to "Luna Powdered Milk 900g",
        "حليب نادك 1لتر" to "Nadec Milk 1L",
        "جبن نادكـ 500 جرام" to "Nadec Cheese 500g",
        "جبن رؤية مثلثات 100 جرام" to "Roya Triangle Cheese 100g",
        
        // Rice
        "رز الرشيد بسمتي هندي 10 كجم" to "Al Rasheed Indian Basmati Rice 10kg",
        "رز اروما سيلا مزة 10 كجم" to "Aroma Sella Mazza Rice 10kg",
        "رز الشعلان مزة 10 كجم" to "Al Shaalan Mazza Rice 10kg",
        "رز العائلة 10 كجم" to "Al Aila Rice 10kg",
        "رز باب الهند 5 كيلو" to "Bab Al Hind Rice 5kg",
        
        // Chicken
        "دجاج سوبريم 1000 جرام" to "Supreme Chicken 1000g",
        "دجاج الوطنية 1000جرام" to "Al Watania Chicken 1000g",
        
        // Tahini
        "طحينية الذهبي فاخر 250 جرام" to "Al Thahabi Premium Tahini 250g",
        
        // Canned Food
        "مكعبات مرقة الدجاج اسناد 24 عبوة" to "Isnad Chicken Stock Cubes 24 Pack",
        "زيتون أسود كوبوليفا 75 جرام" to "Coopoliva Black Olives 75g",
        "زيتــون كوبوليفا 150 جرام" to "Coopoliva Olives 150g",
        "تونا ألوها 158 جرام" to "Aloha Tuna 158g",
        
        // Personal Care
        "شامبو صانسيلك 400 م" to "Sunsilk Shampoo 400ml",
        "شامبو دوف العناية اليومية 400 م" to "Dove Daily Care Shampoo 400ml",
        
        // Snacks
        "فشار البطل صغير" to "Al Batal Small Popcorn",
        
        // Beverages
        "سفن اب 240مل" to "7UP 240ml",
        "حمضيات 240مل" to "Citrus Drink 240ml",
        "عصير القبطان برتقال 250مل" to "Captain Orange Juice 250ml"
    )
    
    /**
     * Get the English translation for an Arabic product name
     * @param arabicName The Arabic product name
     * @return The English translation or the original name if no translation is found
     */
    fun getEnglishName(arabicName: String): String {
        return translations[arabicName] ?: arabicName
    }
    
    /**
     * Check if a translation exists for the given product name
     * @param productName The product name to check
     * @return True if a translation exists, false otherwise
     */
    fun hasTranslation(productName: String): Boolean {
        return translations.containsKey(productName)
    }
    
    /**
     * Get all product translations
     * @return Map of Arabic product names to their English translations
     */
    fun getAllTranslations(): Map<String, String> {
        return translations
    }
}

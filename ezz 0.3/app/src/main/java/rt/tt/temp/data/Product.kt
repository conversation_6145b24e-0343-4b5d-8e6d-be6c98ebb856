package rt.tt.temp.data

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ColumnInfo
import java.util.Date

@Entity(tableName = "products")
data class Product(
    @PrimaryKey(autoGenerate = true)
    val id: Int,
    val productName: String,
    val barcode: String,
    val productType: String,
    val batch: String,
    val branchName: String,
    val currentQuantity: Int,
    val initialQuantity: Int,
    val mfgDate: Long,
    val expireDate: Long,
    val isExpired: Boolean = false,
    val createdAt: Long = System.currentTimeMillis(),
    @ColumnInfo(name = "max_quantity_threshold")
    val maxQuantityThreshold: Int? = null
)

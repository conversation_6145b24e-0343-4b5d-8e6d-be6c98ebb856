package rt.tt.temp.utils

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL
import kotlin.math.max // Import for max function

/**
 * Service for interacting with the Libre Translator API using direct HTTP connections
 * instead of Retrofit to avoid potential compatibility issues
 */
class LibreTranslatorService {
    private val TAG = "LibreTranslatorService"

    // API endpoint - using the official public instance of LibreTranslate
    private val API_URL = "https://libretranslate.com/translate"

    /**
     * Translate text using Libre Translator API
     * @param text The text to translate
     * @param sourceLanguage The source language code
     * @param targetLanguage The target language code
     * @return The translated text or the original text if translation fails
     */
    suspend fun translate(
        text: String,
        sourceLanguage: String,
        targetLanguage: String
    ): String = withContext(Dispatchers.IO) {
        var attempt = 1
        val maxAttempts = 3
        
        while (attempt <= maxAttempts) {
            try {
                // Log the translation request
                Log.d(TAG, "Translating text: '$text' from $sourceLanguage to $targetLanguage (Attempt $attempt)")

                // Map language codes to those supported by Libre Translate
                val mappedSourceLang = mapLanguageCode(sourceLanguage)
                val mappedTargetLang = mapLanguageCode(targetLanguage)

                Log.d(TAG, "Mapped languages: source=$mappedSourceLang, target=$mappedTargetLang")

                // Create connection
                val url = URL(API_URL)
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.setRequestProperty("Content-Type", "application/json")
                connection.setRequestProperty("Accept", "application/json")
                connection.doOutput = true
                connection.connectTimeout = 30000 // 30 seconds
                connection.readTimeout = 30000 // 30 seconds

                // Create JSON request body
                val jsonRequest = JSONObject().apply {
                    put("q", text)
                    put("source", mappedSourceLang)
                    put("target", mappedTargetLang)
                    put("format", "text")
                    put("api_key", "") // No API key needed for this instance
                }

                Log.d(TAG, "Request JSON: ${'$'}{jsonRequest.toString()}")

                // Send request
                val outputStream = connection.outputStream
                val writer = OutputStreamWriter(outputStream)
                writer.write(jsonRequest.toString())
                writer.flush()
                writer.close()

                // Get response
                val responseCode = connection.responseCode
                Log.d(TAG, "Response code: $responseCode")

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    // Read response
                    val inputStream = connection.inputStream
                    val reader = BufferedReader(InputStreamReader(inputStream))
                    val response = StringBuilder()
                    var line: String?
                    while (reader.readLine().also { line = it } != null) {
                        response.append(line)
                    }
                    reader.close()

                    val responseString = response.toString()
                    Log.d(TAG, "Response: $responseString")

                    // Parse JSON response
                    val jsonResponse = JSONObject(responseString)
                    val translatedText = jsonResponse.optString("translatedText", text)

                    // Check if translation was successful
                    if (translatedText.isNotBlank() && translatedText != text) {
                        Log.d(TAG, "Translation successful: '$translatedText'")
                        return@withContext translatedText
                    } else {
                        Log.w(TAG, "Translation returned same text or empty result")
                        
                        // For non-fatal errors, retry
                        if (attempt < maxAttempts) {
                            val delayTime = when (responseCode) {
                                HttpURLConnection.HTTP_UNAVAILABLE -> 5000L
                                HttpURLConnection.HTTP_CLIENT_TIMEOUT, 
                                HttpURLConnection.HTTP_GATEWAY_TIMEOUT -> 10000L
                                else -> 3000L
                            }
                            
                            Log.d(TAG, "Retrying in ${delayTime/1000} seconds... (Attempt ${attempt+1}/$maxAttempts)")
                            Thread.sleep(delayTime)
                            attempt++
                        } else {
                            return@withContext text
                        }
                    }
                } else {
                    // Handle specific HTTP error codes
                    when (responseCode) {
                        HttpURLConnection.HTTP_UNAVAILABLE, // 503 Service Unavailable
                        HttpURLConnection.HTTP_GATEWAY_TIMEOUT -> { // 504 Gateway Timeout
                            if (attempt < maxAttempts) {
                                val delayTime = max(5000L, (5000L * attempt)) // Exponential backoff
                                Log.d(TAG, "Service unavailable. Retrying in ${delayTime/1000} seconds... (Attempt ${attempt+1}/$maxAttempts)")
                                Thread.sleep(delayTime)
                                attempt++
                                continue
                            }
                        }
                        
                        HttpURLConnection.HTTP_CLIENT_TIMEOUT -> { // 408 Request Timeout
                            if (attempt < maxAttempts) {
                                val delayTime = 10000L * attempt // Exponential backoff
                                Log.d(TAG, "Request timeout. Retrying in ${delayTime/1000} seconds... (Attempt ${attempt+1}/$maxAttempts)")
                                Thread.sleep(delayTime)
                                attempt++
                                continue
                            }
                        }
                        
                        else -> Unit // All other HTTP errors will trigger a general retry below
                    }
                    
                    // Read error response
                    val errorStream = connection.errorStream
                    val reader = BufferedReader(InputStreamReader(errorStream))
                    val error = StringBuilder()
                    var line: String?
                    while (reader.readLine().also { line = it } != null) {
                        error.append(line)
                    }
                    reader.close()

                    Log.e(TAG, "Translation failed with code: $responseCode, error: ${error.toString()}")
                    
                    // If we should retry, do so, otherwise return original text
                    if (attempt < maxAttempts) {
                        val delayTime = max(5000L, (3000L * attempt)) // Exponential backoff
                        Log.d(TAG, "Retrying in ${delayTime/1000} seconds... (Attempt ${attempt+1}/$maxAttempts)")
                        Thread.sleep(delayTime)
                        attempt++
                    } else {
                        return@withContext text
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Translation attempt $attempt failed: ${e.message}", e)
                
                if (attempt < maxAttempts) {
                    val delayTime = when (e) {
                        is java.net.SocketTimeoutException -> 10000L * attempt // Longer delay for timeout
                        is java.net.UnknownHostException -> 30000L // Very long delay for network issues
                        else -> 5000L * attempt // Standard delay with exponential backoff
                    }
                    
                    Log.d(TAG, "Retrying in ${delayTime/1000} seconds... (Attempt ${attempt+1}/$maxAttempts)")
                    Thread.sleep(delayTime)
                    attempt++
                } else {
                    Log.e(TAG, "All translation attempts failed")
                    return@withContext text
                }
            }
        }
        
        // If we get here, all attempts have failed
        Log.e(TAG, "Translation failed after $maxAttempts attempts")
        text
    }

    /**
     * Map language codes to those supported by Libre Translate
     * Libre Translate uses ISO 639-1 codes
     */
    private fun mapLanguageCode(code: String): String {
        // First normalize the code to lowercase and trim
        val normalizedCode = code.lowercase().trim()

        return when (normalizedCode) {
            "en" -> "en" // English
            "bn" -> "bn" // Bengali
            "ar" -> "ar" // Arabic
            // Add more mappings if needed
            else -> {
                // Log when we encounter an unknown language code
                Log.w(TAG, "Unknown language code: $code, defaulting to English")
                "en" // Default to English
            }
        }
    }

    /**
     * Check if Libre Translate supports a language
     */
    fun isLanguageSupported(languageCode: String): Boolean {
        // These are the languages supported by LibreTranslate
        val supportedLanguages = listOf(
            "en", "ar", "zh", "nl", "fi", "fr", "de", "hi", "hu",
            "id", "ga", "it", "ja", "ko", "pl", "pt", "ru", "es",
            "sv", "tr", "uk", "vi", "bn"
        )
        val mappedCode = mapLanguageCode(languageCode)
        return supportedLanguages.contains(mappedCode)
    }
}
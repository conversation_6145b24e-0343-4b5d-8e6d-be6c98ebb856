package rt.tt.temp

import android.os.Bundle
import android.util.Log
import android.app.Activity
import androidx.activity.compose.setContent
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.foundation.layout.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.navigation.compose.rememberNavController
import rt.tt.temp.ui.base.BaseActivity
import rt.tt.temp.ui.theme.TempTheme
import rt.tt.temp.data.AppDatabase
import rt.tt.temp.data.PreferencesManager
import rt.tt.temp.navigation.Screen
import rt.tt.temp.navigation.NavigationItem
// Using SetupNavGraph instead of NavGraph
import rt.tt.temp.navigation.SetupNavGraph
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.automirrored.filled.List
import androidx.compose.material.icons.filled.Speed
import androidx.compose.material.icons.filled.Translate
import kotlinx.coroutines.launch
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import rt.tt.temp.ui.theme.ThemeViewModel
import rt.tt.temp.ui.viewmodels.TranslationViewModel
import rt.tt.temp.ui.components.LanguageSelectionDialog
import rt.tt.temp.ui.components.TranslationProviderDialog
import rt.tt.temp.R
import androidx.compose.ui.res.stringResource
import androidx.activity.result.contract.ActivityResultContracts
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.common.api.Scope
import com.google.android.material.snackbar.Snackbar

@OptIn(ExperimentalMaterial3Api::class)
class MainActivity : BaseActivity() {
    private val TAG = "MainActivity"
    private val themeViewModel: ThemeViewModel by viewModels()
    // Use viewModels() with retained = true to ensure the ViewModel survives configuration changes
    private val translationViewModel: TranslationViewModel by viewModels { defaultViewModelProviderFactory }
    private lateinit var database: AppDatabase
    private lateinit var preferencesManager: PreferencesManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        database = AppDatabase.getInstance(this)
        preferencesManager = PreferencesManager(this)

        lifecycleScope.launch {
            preferencesManager.darkModeFlow.collect { isDarkMode ->
                themeViewModel.setDarkTheme(isDarkMode)
            }
        }

        setContent {
            val isDarkTheme = themeViewModel.isDarkTheme.collectAsState()

            TempTheme(
                darkTheme = isDarkTheme.value
            ) {
                val navController = rememberNavController()
                val drawerState = rememberDrawerState(initialValue = DrawerValue.Closed)
                val scope = rememberCoroutineScope()
                var currentRoute by remember { mutableStateOf(Screen.Dashboard.route) }

                val navigationItems = listOf(
                    NavigationItem(
                        title = stringResource(R.string.nav_dashboard),
                        route = Screen.Dashboard.route,
                        icon = Icons.Default.Dashboard
                    ),
                    NavigationItem(
                        title = stringResource(R.string.nav_all_items),
                        route = Screen.AllItems.route,
                        icon = Icons.AutoMirrored.Filled.List
                    ),
                    NavigationItem(
                        title = stringResource(R.string.nav_new_entry),
                        route = Screen.NewEntry.route,
                        icon = Icons.Default.Add
                    ),
                    NavigationItem(
                        title = stringResource(R.string.nav_expired_list),
                        route = Screen.ExpiredList.route,
                        icon = Icons.Default.Warning
                    ),
                    NavigationItem(
                        title = stringResource(R.string.nav_threshold),
                        route = Screen.Threshold.route,
                        icon = Icons.Default.Speed
                    ),
                    NavigationItem(
                        title = stringResource(R.string.nav_export),
                        route = Screen.Export.route,
                        icon = Icons.Default.Share
                    ),
                    NavigationItem(
                        title = "Google Drive Sync",
                        route = Screen.GoogleDriveSync.route,
                        icon = Icons.Default.Cloud
                    ),
                    NavigationItem(
                        title = stringResource(R.string.nav_settings),
                        route = Screen.Settings.route,
                        icon = Icons.Default.Settings
                    ),
                    NavigationItem(
                        title = stringResource(R.string.nav_about),
                        route = Screen.About.route,
                        icon = Icons.Default.Info
                    )
                )

                navController.addOnDestinationChangedListener { _, destination, _ ->
                    currentRoute = destination.route ?: Screen.Dashboard.route
                }

                ModalNavigationDrawer(
                    drawerState = drawerState,
                    drawerContent = {
                        ModalDrawerSheet {
                            Spacer(modifier = Modifier.height(12.dp))
                            // Main navigation items
                            navigationItems.forEach { item ->
                                NavigationDrawerItem(
                                    icon = { Icon(item.icon, contentDescription = null) },
                                    label = { Text(item.title) },
                                    selected = currentRoute == item.route,
                                    onClick = {
                                        scope.launch {
                                            drawerState.close()
                                            navController.navigate(item.route) {
                                                popUpTo(navController.graph.startDestinationId)
                                                launchSingleTop = true
                                            }
                                        }
                                    },
                                    modifier = Modifier.padding(NavigationDrawerItemDefaults.ItemPadding)
                                )
                            }

                            // Upcoming Features Section
                            HorizontalDivider(
                                modifier = Modifier.padding(vertical = 12.dp)
                            )

                            Text(
                                text = stringResource(R.string.upcoming_features),
                                style = MaterialTheme.typography.titleSmall,
                                color = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.padding(start = 24.dp, bottom = 8.dp)
                            )

                            // Upcoming feature items
                            listOf(
                                NavigationItem(
                                    title = "${stringResource(R.string.nav_analytics)} - ${stringResource(R.string.coming_soon)}",
                                    route = Screen.Analytics.route,
                                    icon = Icons.Default.BarChart
                                ),
                                NavigationItem(
                                    title = "${stringResource(R.string.nav_attendance)} - ${stringResource(R.string.coming_soon)}",
                                    route = Screen.Attendance.route,
                                    icon = Icons.Default.People
                                ),
                                NavigationItem(
                                    title = "${stringResource(R.string.nav_incentive)} - ${stringResource(R.string.coming_soon)}",
                                    route = Screen.Incentive.route,
                                    icon = Icons.Default.Star
                                )
                            ).forEach { item ->
                                NavigationDrawerItem(
                                    icon = {
                                        Icon(
                                            item.icon,
                                            contentDescription = null,
                                            tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                                        )
                                    },
                                    label = {
                                        Text(
                                            item.title,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                                        )
                                    },
                                    selected = false,
                                    onClick = { /* Disabled */ },
                                    modifier = Modifier.padding(NavigationDrawerItemDefaults.ItemPadding)
                                )
                            }
                        }
                    }
                ) {
                    Scaffold(
                        topBar = {
                            TopAppBar(
                                title = {
                                    Text(
                                        text = navigationItems.find { it.route == currentRoute }?.title
                                            ?: stringResource(R.string.nav_dashboard)
                                    )
                                },
                                navigationIcon = {
                                    IconButton(onClick = { scope.launch { drawerState.open() } }) {
                                        Icon(Icons.Default.Menu, contentDescription = null)
                                    }
                                },
                                actions = {
                                    // Only show translation button on Dashboard, All Items, and Expired List screens
                                    if (currentRoute == Screen.Dashboard.route ||
                                        currentRoute == Screen.AllItems.route ||
                                        currentRoute == Screen.ExpiredList.route) {
                                        val isTranslationActive by translationViewModel.isTranslationActive.collectAsState()
                                        IconButton(onClick = { translationViewModel.toggleTranslation() }) {
                                            Icon(
                                                imageVector = Icons.Default.Translate,
                                                contentDescription = "Translate",
                                                tint = if (isTranslationActive) {
                                                    MaterialTheme.colorScheme.primary
                                                } else {
                                                    MaterialTheme.colorScheme.onSurface
                                                }
                                            )
                                        }

                                        // Show language selection dialog when needed
                                        val showLanguageDialog = translationViewModel.showLanguageDialog.collectAsState()
                                        if (showLanguageDialog.value) {
                                            LanguageSelectionDialog(
                                                translationViewModel = translationViewModel,
                                                onDismiss = { translationViewModel.hideLanguageDialog() }
                                            )
                                        }

                                        // Show provider selection dialog when needed
                                        val showProviderDialog = translationViewModel.showProviderDialog.collectAsState()
                                        if (showProviderDialog.value) {
                                            TranslationProviderDialog(
                                                translationViewModel = translationViewModel,
                                                onDismiss = { translationViewModel.hideProviderDialog() }
                                            )
                                        }
                                    }
                                }
                            )
                        }
                    ) { paddingValues ->
                        Surface(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(paddingValues),
                            color = MaterialTheme.colorScheme.background
                        ) {
                            // Use SetupNavGraph instead of NavGraph
                            SetupNavGraph(
                                navController = navController,
                                translationViewModel = translationViewModel
                            )
                        }
                    }
                }
            }
        }
    }

    // Add these fields for Google Drive integration
    private var googleDriveSignInCallback: ((Boolean) -> Unit)? = null
    private val googleDriveSignInResult = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.let { data ->
                GoogleSignIn.getSignedInAccountFromIntent(data)
                    .addOnSuccessListener { account ->
                        // Successfully signed in
                        Log.d(TAG, "Google Drive sign-in successful: ${account.email}")
                        Snackbar.make(
                            window.decorView.rootView,
                            "Connected to Google Drive",
                            Snackbar.LENGTH_SHORT
                        ).show()
                        googleDriveSignInCallback?.invoke(true)
                    }
                    .addOnFailureListener { exception ->
                        // Sign in failed
                        Log.e(TAG, "Google Drive sign-in failed", exception)
                        Snackbar.make(
                            window.decorView.rootView,
                            "Drive connection failed",
                            Snackbar.LENGTH_SHORT
                        ).show()
                        googleDriveSignInCallback?.invoke(false)
                    }
            }
        } else {
            Log.d(TAG, "Google Drive sign-in cancelled")
            Snackbar.make(
                window.decorView.rootView,
                "Drive connection cancelled",
                Snackbar.LENGTH_SHORT
            ).show()
            googleDriveSignInCallback?.invoke(false)
        }
    }

    fun startGoogleDriveSignIn(callback: (Boolean) -> Unit) {
        this.googleDriveSignInCallback = callback
        // The actual sign-in intent will be started by the activity result launcher
    }

}

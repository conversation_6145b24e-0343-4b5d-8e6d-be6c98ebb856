package rt.tt.temp.ui.screens

import android.view.animation.OvershootInterpolator
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.*
import androidx.compose.animation.fadeIn
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.lottie.compose.*
import kotlinx.coroutines.delay

@Composable
fun SplashScreen(onSplashComplete: () -> Unit) {
    var startAnimation by remember { mutableStateOf(false) }
    val density = LocalDensity.current
    
    // Animation states
    val alphaAnim by animateFloatAsState(
        targetValue = if (startAnimation) 1f else 0f,
        animationSpec = tween(
            durationMillis = 300,  // Reduced from 500
            easing = FastOutSlowInEasing
        ),
        label = ""
    )
    
    val scale by animateFloatAsState(
        targetValue = if (startAnimation) 1f else 0.5f,
        animationSpec = tween(
            durationMillis = 300,  // Reduced from 500
            easing = EaseOutBack
        ),
        label = ""
    )

    val slideAnim by animateIntAsState(
        targetValue = if (startAnimation) 0 else with(density) { -50.dp.roundToPx() },
        animationSpec = tween(
            durationMillis = 300,  // Reduced from 1000
            easing = FastOutSlowInEasing
        ),
        label = ""
    )

    // Lottie animation state
    val composition by rememberLottieComposition(
        LottieCompositionSpec.Asset("animations/clock_calendar.json")
    )
    val progress by animateLottieCompositionAsState(
        composition = composition,
        iterations = LottieConstants.IterateForever,
    )

    // Start animations
    LaunchedEffect(key1 = true) {
        startAnimation = true
        delay(800)  // Reduced from 1500
        onSplashComplete()
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.padding(16.dp)
        ) {
            // Lottie Animation
            AnimatedVisibility(
                visible = startAnimation,
                enter = fadeIn(
                    animationSpec = tween(300)  // Reduced from 1000
                )
            ) {
                LottieAnimation(
                    composition = composition,
                    progress = { progress },
                    modifier = Modifier
                        .size(240.dp)
                        .scale(scale)
                )
            }

            Spacer(modifier = Modifier.height(32.dp))

            // App Name - First Part
            Text(
                text = "Inventory",
                color = MaterialTheme.colorScheme.primary,
                fontSize = 40.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .scale(scale)
                    .alpha(alphaAnim)
                    .offset(y = slideAnim.dp)
            )

            // App Name - Second Part
            Text(
                text = "Expiry Tracker",
                color = MaterialTheme.colorScheme.secondary,
                fontSize = 32.sp,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .scale(scale)
                    .alpha(alphaAnim)
                    .offset(y = slideAnim.dp)
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Subtitle
            AnimatedVisibility(
                visible = startAnimation,
                enter = fadeIn(
                    animationSpec = tween(300, delayMillis = 150)  // Reduced from 1000, 500
                ) + slideInVertically(
                    animationSpec = tween(300),  // Reduced from 1000
                    initialOffsetY = { it / 2 }
                )
            ) {
                Text(
                    text = "Track your inventory expiry dates",
                    color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f),
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = 32.dp)
                )
            }
        }
    }
}
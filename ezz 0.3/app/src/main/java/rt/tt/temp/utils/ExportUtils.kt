package rt.tt.temp.utils

import android.content.Context
import android.util.Log
import org.apache.poi.ss.usermodel.*
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import rt.tt.temp.data.Product
import java.io.File
import java.io.FileOutputStream
import java.io.FileWriter
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

object ExportUtils {
    private const val TAG = "ExportUtils"
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

    // Replace the expiry calculations with our helper function
    fun exportProductsToExcel(products: List<Product>, directory: File): File {
        Log.d(TAG, "Starting Excel export with ${products.size} products to directory: ${directory.absolutePath}")

        if (products.isEmpty()) {
            throw IllegalArgumentException("No products available to export")
        }

        if (!directory.exists() && !directory.mkdirs()) {
            throw IOException("Failed to create directory: ${directory.absolutePath}")
        }

        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val fileName = "inventory_$timestamp.xlsx"
        val excelFile = File(directory, fileName)
        val currentDate = Date()

        // Extract time values from currentDate
        val currentYear = currentDate.year + 1900 // Java Date year is years since 1900
        val currentMonth = currentDate.month + 1 // Java Date month is 0-based
        val currentDay = currentDate.date

        Log.d(TAG, "Creating Excel file at: ${excelFile.absolutePath}")

        XSSFWorkbook().use { workbook ->
            val sheet = workbook.createSheet("Inventory")

            // Create header style
            val headerStyle = workbook.createCellStyle().apply {
                fillForegroundColor = IndexedColors.GREY_25_PERCENT.index
                fillPattern = FillPatternType.SOLID_FOREGROUND
                borderTop = BorderStyle.THIN
                borderBottom = BorderStyle.THIN
                borderLeft = BorderStyle.THIN
                borderRight = BorderStyle.THIN
                wrapText = true  // Enable text wrapping
                verticalAlignment = VerticalAlignment.CENTER  // Center text vertically
            }

            // Create data cell style with borders and text wrapping
            val dataCellStyle = workbook.createCellStyle().apply {
                borderTop = BorderStyle.THIN
                borderBottom = BorderStyle.THIN
                borderLeft = BorderStyle.THIN
                borderRight = BorderStyle.THIN
                wrapText = true  // Enable text wrapping
                verticalAlignment = VerticalAlignment.CENTER  // Center text vertically
            }

            // Create header row
            val headerRow = sheet.createRow(0)
            val headers = listOf(
                "Product Name",
                "Branch Name",
                "Product Type",
                "Barcode",
                "Batch",
                "Initial Quantity",
                "Current Quantity",
                "Manufacturing Date",
                "Expiry Date",
                "Created Date",
                "Status"
            )

            headers.forEachIndexed { index, header ->
                headerRow.createCell(index).apply {
                    setCellValue(header)
                    cellStyle = headerStyle
                }
            }

            // Add data rows
            products.forEachIndexed { index, product ->
                try {
                    val row = sheet.createRow(index + 1)
                    val daysUntilExpiry = daysUntilExpiry(product, currentDate)

                    val status = when {
                        product.isExpired -> "Expired"
                        daysUntilExpiry < 0 -> "Expired"
                        daysUntilExpiry < 30 -> "Critical"
                        daysUntilExpiry < 90 -> "Warning"
                        else -> "Good"
                    }

                    row.apply {
                        // Create cells with data and apply style
                        createCell(0).apply {
                            setCellValue(product.productName)
                            cellStyle = dataCellStyle
                        }
                        createCell(1).apply {
                            setCellValue(product.branchName)
                            cellStyle = dataCellStyle
                        }
                        createCell(2).apply {
                            setCellValue(product.productType)
                            cellStyle = dataCellStyle
                        }
                        createCell(3).apply {
                            setCellValue(product.barcode)
                            cellStyle = dataCellStyle
                        }
                        createCell(4).apply {
                            setCellValue(product.batch)
                            cellStyle = dataCellStyle
                        }
                        createCell(5).apply {
                            setCellValue(product.initialQuantity.toString())
                            cellStyle = dataCellStyle
                        }
                        createCell(6).apply {
                            setCellValue(product.currentQuantity.toString())
                            cellStyle = dataCellStyle
                        }
                        createCell(7).apply {
                            setCellValue(product.mfgDate?.let { dateFormat.format(it) } ?: "")
                            cellStyle = dataCellStyle
                        }
                        createCell(8).apply {
                            setCellValue(product.expireDate?.let { dateFormat.format(it) } ?: "")
                            cellStyle = dataCellStyle
                        }
                        createCell(9).apply {
                            setCellValue(dateFormat.format(Date(product.createdAt)))
                            cellStyle = dataCellStyle
                        }
                        createCell(10).apply {
                            setCellValue(status)
                            cellStyle = dataCellStyle
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error writing row ${index + 1}", e)
                }
            }

            // Set column widths and auto-size
            sheet.setColumnWidth(0, 30 * 256)  // Product Name - wider column
            sheet.setColumnWidth(1, 15 * 256)  // Branch Name
            sheet.setColumnWidth(2, 15 * 256)  // Product Type
            sheet.setColumnWidth(3, 15 * 256)  // Barcode
            sheet.setColumnWidth(4, 15 * 256)  // Batch
            (5..10).forEach { sheet.autoSizeColumn(it) }  // Auto-size remaining columns

            // Set row heights for better readability with wrapped text
            (0..products.size).forEach { rowIndex ->
                val row = sheet.getRow(rowIndex)
                if (row != null) {
                    // Convert to Int first, then to Short to avoid ambiguous conversion
                    val newHeight = (row.height * 1.5).toInt().toShort()
                    row.height = newHeight
                } else {
                    // Default height if row is null
                    sheet.getRow(rowIndex)?.height = 400
                }
            }

            // Write to file
            FileOutputStream(excelFile).use { fos ->
                workbook.write(fos)
            }
        }

        Log.d(TAG, "Excel export completed successfully")
        return excelFile

    }

    // This function is used for both CSV and Excel exports
    fun exportProductsToCSV(products: List<Product>, directory: File): File {
        return exportProductsToCSVInternal(products, directory, "csv")
    }

    // Export products to JSON format
    fun exportProductsToJSON(products: List<Product>, directory: File): File {
        if (!directory.exists()) {
            directory.mkdirs()
        }

        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val fileName = "inventory_$timestamp.json"
        val jsonFile = File(directory, fileName)

        // Use the JSON utility class to convert products to JSON
        val jsonContent = JSON.exportProductsToJson(products)

        // Write to file
        jsonFile.writeText(jsonContent)

        return jsonFile
    }

    // Internal implementation for CSV export
    private fun exportProductsToCSVInternal(products: List<Product>, directory: File, extension: String): File {
        if (!directory.exists()) {
            directory.mkdirs()
        }

        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
            .format(Date())
        val fileName = "inventory_$timestamp.$extension"
        val csvFile = File(directory, fileName)
        val currentDate = Date()

        FileWriter(csvFile).use { writer ->
            // Write header
            writer.append("Product Name,Branch Name,Product Type,Barcode,Batch,Initial Quantity," +
                "Current Quantity,Manufacturing Date,Expiry Date,Created Date,Status\n")

            // Write data
            products.forEach { product ->
                val daysUntilExpiry = daysUntilExpiry(product, currentDate)

                val status = when {
                    product.isExpired -> "Expired"
                    daysUntilExpiry < 0 -> "Expired"
                    daysUntilExpiry < 30 -> "Critical"
                    daysUntilExpiry < 90 -> "Warning"
                    else -> "Good"
                }

                // Properly escape fields for CSV format
                fun escapeCSV(field: String): String {
                    // If the field contains commas, quotes, or newlines, wrap in quotes and escape any quotes
                    return if (field.contains(",") || field.contains("\"") || field.contains("\n")) {
                        "\"${field.replace("\"", "\"\"")}\"" // Replace quotes with double quotes for escaping
                    } else {
                        field
                    }
                }

                // Format each row with proper CSV escaping
                val row = listOf(
                    escapeCSV(product.productName),
                    escapeCSV(product.branchName),
                    escapeCSV(product.productType),
                    escapeCSV(product.barcode),
                    escapeCSV(product.batch),
                    product.initialQuantity.toString(),
                    product.currentQuantity.toString(),
                    escapeCSV(product.mfgDate?.let { dateFormat.format(it) } ?: ""),
                    escapeCSV(product.expireDate?.let { dateFormat.format(it) } ?: ""),
                    escapeCSV(dateFormat.format(Date(product.createdAt))),
                    escapeCSV(status)
                ).joinToString(",")

                writer.append(row + "\n")
            }
        }

        return csvFile
    }

    // Add helper function for days until expiry calculation
    private fun daysUntilExpiry(product: Product, currentDate: Date): Int {
        return ((product.expireDate - currentDate.time) / (1000 * 60 * 60 * 24)).toInt()
    }
}

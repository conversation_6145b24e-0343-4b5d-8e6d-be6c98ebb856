package rt.tt.temp.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Lock
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import rt.tt.temp.ui.viewmodels.Language
import rt.tt.temp.ui.viewmodels.TranslationViewModel

/**
 * Dialog for selecting a language for translation
 */
@Composable
fun LanguageSelectionDialog(
    translationViewModel: TranslationViewModel,
    onDismiss: () -> Unit
) {
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Select Language",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.height(16.dp))

                // English option
                LanguageOption(
                    language = Language.ENGLISH,
                    onClick = { translationViewModel.setLanguage(Language.ENGLISH) },
                    translationViewModel = translationViewModel
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Bangla option (locked)
                LanguageOption(
                    language = Language.BANGLA,
                    onClick = { translationViewModel.setLanguage(Language.BANGLA) },
                    translationViewModel = translationViewModel,
                    isLocked = true
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Arabic option
                LanguageOption(
                    language = Language.ARABIC,
                    onClick = { translationViewModel.setLanguage(Language.ARABIC) },
                    translationViewModel = translationViewModel
                )

                Spacer(modifier = Modifier.height(16.dp))

                TextButton(
                    onClick = onDismiss,
                    modifier = Modifier.align(Alignment.End)
                ) {
                    Text("Cancel")
                }
            }
        }
    }
}

@Composable
private fun LanguageOption(
    language: Language,
    onClick: () -> Unit,
    translationViewModel: TranslationViewModel = androidx.lifecycle.viewmodel.compose.viewModel(),
    isLocked: Boolean = false
) {
    val currentLanguage by translationViewModel.targetLanguage.collectAsState()
    val isSelected = language == currentLanguage

    Button(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth(),
        colors = ButtonDefaults.buttonColors(
            containerColor = when {
                isLocked -> MaterialTheme.colorScheme.surfaceVariant
                isSelected -> MaterialTheme.colorScheme.primary
                else -> MaterialTheme.colorScheme.primaryContainer
            },
            contentColor = when {
                isLocked -> MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                isSelected -> MaterialTheme.colorScheme.onPrimary
                else -> MaterialTheme.colorScheme.onPrimaryContainer
            }
        ),
        enabled = !isLocked
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = language.displayName,
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.padding(vertical = 8.dp)
            )

            when {
                isLocked -> {
                    Icon(
                        imageVector = Icons.Default.Lock,
                        contentDescription = "Locked",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                    )
                }
                isSelected -> {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "Selected",
                        tint = MaterialTheme.colorScheme.onPrimary
                    )
                }
            }
        }
    }
}

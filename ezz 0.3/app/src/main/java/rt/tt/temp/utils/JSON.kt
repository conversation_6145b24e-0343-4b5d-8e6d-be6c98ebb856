package rt.tt.temp.utils

import org.json.JSONObject
import org.json.JSONArray
import rt.tt.temp.data.Product
import java.util.Date

class JSON {
    companion object {
        /**
         * Converts a list of Product objects to a JSON string
         * Format matches the backup file format
         */
        fun exportProductsToJson(products: List<Product>): String {
            val json = JSONObject()
            json.put("backup_date", System.currentTimeMillis())
            json.put("version", "1.0")

            // Create products array
            val productsArray = JSONArray()
            for (product in products) {
                val jsonObject = JSONObject().apply {
                    put("id", product.id)
                    put("productName", product.productName)
                    put("branchName", product.branchName)
                    put("productType", product.productType)
                    put("barcode", product.barcode)
                    put("batch", product.batch)
                    put("initialQuantity", product.initialQuantity)
                    put("currentQuantity", product.currentQuantity)
                    put("mfgDate", product.mfgDate) // This is already a Long
                    put("expireDate", product.expireDate) // This is already a Long
                    put("createdAt", product.createdAt) // This is already a Long
                    put("isExpired", product.isExpired)
                }
                productsArray.put(jsonObject)
            }

            // Add products array to main JSON object
            json.put("products", productsArray)

            // Return pretty-printed JSON with indentation
            return json.toString(2)
        }

        /**
         * Converts a single Product object to a JSON string
         */
        fun productToJson(product: Product): String {
            val jsonObject = JSONObject().apply {
                put("id", product.id)
                put("productName", product.productName)
                put("branchName", product.branchName)
                put("productType", product.productType)
                put("barcode", product.barcode)
                put("batch", product.batch)
                put("initialQuantity", product.initialQuantity)
                put("currentQuantity", product.currentQuantity)
                put("mfgDate", product.mfgDate)
                put("expireDate", product.expireDate)
                put("createdAt", product.createdAt)
                put("isExpired", product.isExpired)
            }

            return jsonObject.toString()
        }

        /**
         * Converts a JSON string back to a list of Product objects
         */
        fun jsonToProducts(json: String): List<Product> {
            val jsonArray = JSONArray(json)
            val products = mutableListOf<Product>()

            for (i in 0 until jsonArray.length()) {
                val jsonObject = jsonArray.getJSONObject(i)

                val product = Product(
                    id = jsonObject.getInt("id"),
                    productName = jsonObject.getString("productName"),
                    branchName = jsonObject.getString("branchName"),
                    productType = jsonObject.getString("productType"),
                    barcode = jsonObject.getString("barcode"),
                    batch = jsonObject.getString("batch"),
                    initialQuantity = jsonObject.getInt("initialQuantity"),
                    currentQuantity = jsonObject.getInt("currentQuantity"),
                    mfgDate = jsonObject.getLong("mfgDate"),
                    expireDate = jsonObject.getLong("expireDate"),
                    createdAt = jsonObject.getLong("createdAt"),
                    isExpired = jsonObject.getBoolean("isExpired")
                )

                products.add(product)
            }

            return products
        }
    }
}
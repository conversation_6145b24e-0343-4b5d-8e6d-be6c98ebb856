package rt.tt.temp.ui.viewmodels

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import rt.tt.temp.data.Product
import rt.tt.temp.utils.ExportType
import rt.tt.temp.utils.exportFile
import java.io.File

class ExportViewModel : ViewModel() {
    private val _exportState = MutableStateFlow<ExportState>(ExportState.Idle)
    val exportState = _exportState.asStateFlow()

    fun exportToExcel(context: Context, products: List<Product>) {
        viewModelScope.launch(Dispatchers.IO) {
            _exportState.value = ExportState.Loading

            try {
                val file = context.exportFile(products, ExportType.EXCEL)
                withContext(Dispatchers.Main) {
                    _exportState.value = ExportState.Success(file)
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    _exportState.value = ExportState.Error(e.message ?: "Export failed")
                }
            }
        }
    }

    // Helper function to update export state from outside the ViewModel
    fun updateExportState(state: ExportState) {
        _exportState.value = state
    }

    override fun onCleared() {
        super.onCleared()
        // Reset state when ViewModel is cleared
        _exportState.value = ExportState.Idle
    }
}

sealed class ExportState {
    object Idle : ExportState()
    object Loading : ExportState()
    data class Success(val file: File) : ExportState()
    data class Error(val message: String) : ExportState()
}
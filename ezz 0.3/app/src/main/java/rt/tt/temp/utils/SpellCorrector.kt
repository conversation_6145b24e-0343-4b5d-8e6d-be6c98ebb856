package rt.tt.temp.utils

import android.content.Context
import android.util.Log
import java.io.BufferedReader
import java.io.InputStreamReader
import java.util.*

/**
 * A custom spell corrector inspired by SymSpell algorithm
 * but implemented without external dependencies
 */
class SpellCorrector(private val context: Context) {
    private val TAG = "SpellCorrector"

    // Dictionary of words and their frequencies
    private val dictionary = mutableMapOf<String, Int>()

    // Maximum edit distance for corrections
    private val maxEditDistance = 2

    // Initialize dictionaries
    init {
        loadDictionaries()
    }

    /**
     * Load dictionaries for different languages
     */
    private fun loadDictionaries() {
        try {
            // Load English dictionary
            loadDictionary("en_dictionary.txt", "en")

            // Load common words for English if dictionary loading fails
            if (dictionary.isEmpty()) {
                addCommonEnglishWords()
            }

            Log.d(TAG, "Loaded ${dictionary.size} words into dictionary")
        } catch (e: Exception) {
            Log.e(TAG, "Error loading dictionaries: ${e.message}", e)
            // Add common words as fallback
            addCommonEnglishWords()
        }
    }

    /**
     * Load a dictionary file from assets
     */
    private fun loadDictionary(fileName: String, languageCode: String) {
        try {
            val assetManager = context.assets
            val inputStream = assetManager.open(fileName)
            val reader = BufferedReader(InputStreamReader(inputStream))
            var line: String?

            while (reader.readLine().also { line = it } != null) {
                line?.trim()?.let {
                    if (it.isNotEmpty()) {
                        // Format is expected to be "word,frequency"
                        val parts = it.split(",")
                        if (parts.size >= 2) {
                            val word = parts[0].trim()
                            val frequency = parts[1].trim().toIntOrNull() ?: 1
                            dictionary[word] = frequency
                        } else {
                            dictionary[it] = 1
                        }
                    }
                }
            }

            reader.close()
            Log.d(TAG, "Loaded dictionary for $languageCode")
        } catch (e: Exception) {
            Log.e(TAG, "Error loading dictionary for $languageCode: ${e.message}", e)
        }
    }

    /**
     * Add common English words to the dictionary
     */
    private fun addCommonEnglishWords() {
        val commonWords = arrayOf(
            "the", "be", "to", "of", "and", "a", "in", "that", "have", "I",
            "it", "for", "not", "on", "with", "he", "as", "you", "do", "at",
            "this", "but", "his", "by", "from", "they", "we", "say", "her", "she",
            "or", "an", "will", "my", "one", "all", "would", "there", "their", "what",
            "so", "up", "out", "if", "about", "who", "get", "which", "go", "me",
            "product", "item", "food", "drink", "milk", "water", "juice", "tea", "coffee",
            "rice", "flour", "sugar", "salt", "oil", "spice", "meat", "chicken", "fish",
            "vegetable", "fruit", "bread", "cheese", "egg", "butter", "yogurt",
            "clean", "wash", "soap", "detergent", "shampoo", "toothpaste", "tissue",
            "paper", "plastic", "glass", "metal", "wood", "cloth", "cotton", "wool"
        )

        commonWords.forEach { word ->
            dictionary[word] = 100
        }

        Log.d(TAG, "Added ${commonWords.size} common English words to dictionary")
    }

    /**
     * Correct a word using a simplified version of the SymSpell algorithm
     */
    fun correctWord(word: String): String {
        // Don't try to correct very short words or words with numbers/special chars
        if (word.length <= 2 || word.matches(Regex(".*[0-9!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>/?].*"))) {
            return word
        }

        // If the word is in the dictionary, it's correct
        if (dictionary.containsKey(word.lowercase())) {
            return word
        }

        // Find candidates with edit distance <= maxEditDistance
        val candidates = findCandidates(word.lowercase())

        // If no candidates found, return the original word
        if (candidates.isEmpty()) {
            return word
        }

        // Return the candidate with the highest frequency
        return candidates.maxByOrNull { (_, frequency) -> frequency }?.key ?: word
    }

    /**
     * Find candidate corrections for a word
     */
    private fun findCandidates(word: String): Map<String, Int> {
        val candidates = mutableMapOf<String, Int>()

        // Check all words in the dictionary
        for ((dictWord, frequency) in dictionary) {
            // Skip words with length difference > maxEditDistance
            if (Math.abs(dictWord.length - word.length) > maxEditDistance) {
                continue
            }

            // Calculate edit distance
            val distance = calculateEditDistance(word, dictWord)

            // Add to candidates if distance <= maxEditDistance
            if (distance <= maxEditDistance) {
                candidates[dictWord] = frequency
            }
        }

        return candidates
    }

    /**
     * Calculate Levenshtein edit distance between two strings
     */
    private fun calculateEditDistance(s1: String, s2: String): Int {
        val m = s1.length
        val n = s2.length

        // Create a matrix of size (m+1) x (n+1)
        val dp = Array(m + 1) { IntArray(n + 1) }

        // Initialize the matrix
        for (i in 0..m) {
            dp[i][0] = i
        }

        for (j in 0..n) {
            dp[0][j] = j
        }

        // Fill the matrix
        for (i in 1..m) {
            for (j in 1..n) {
                val cost = if (s1[i - 1] == s2[j - 1]) 0 else 1
                dp[i][j] = minOf(
                    dp[i - 1][j] + 1,      // deletion
                    dp[i][j - 1] + 1,      // insertion
                    dp[i - 1][j - 1] + cost // substitution
                )
            }
        }

        return dp[m][n]
    }

    /**
     * Correct text by correcting each word
     */
    fun correctText(text: String): String {
        // Split text into words
        val words = text.split(Regex("\\s+"))
        val correctedWords = mutableListOf<String>()

        // Correct each word
        for (word in words) {
            if (word.isBlank()) {
                correctedWords.add(word)
                continue
            }

            val correctedWord = correctWord(word)
            correctedWords.add(correctedWord)
        }

        // Join corrected words
        return correctedWords.joinToString(" ")
    }
}

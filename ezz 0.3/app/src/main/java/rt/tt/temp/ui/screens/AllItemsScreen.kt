package rt.tt.temp.ui.screens

import androidx.compose.ui.platform.LocalContext
import rt.tt.temp.utils.TextToSpeechHelper

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import rt.tt.temp.data.AppDatabase
import rt.tt.temp.data.Product
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Remove
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.ExpandLess
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material.icons.filled.QrCodeScanner
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Sort
import androidx.compose.material.icons.filled.Translate
import androidx.compose.material.icons.filled.VolumeUp
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.input.KeyboardType
import androidx.activity.compose.rememberLauncherForActivityResult
import com.journeyapps.barcodescanner.ScanContract
import com.journeyapps.barcodescanner.ScanOptions
import rt.tt.temp.utils.BarcodeScanner
import kotlinx.coroutines.launch
import androidx.compose.runtime.rememberCoroutineScope
import android.util.Log
import java.util.Date
import java.text.SimpleDateFormat
import java.util.Locale
import kotlinx.coroutines.delay
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import androidx.compose.ui.res.stringResource
import rt.tt.temp.R
import androidx.compose.ui.graphics.Color
import androidx.annotation.StringRes
import android.content.Context
import androidx.compose.runtime.remember
import androidx.compose.runtime.DisposableEffect
import rt.tt.temp.ui.viewmodels.TranslationViewModel
import rt.tt.temp.ui.components.TranslatableText

@Composable
fun AllItemsScreen(
    products: List<Product>,
    database: AppDatabase,
    currentDate: Date,
    translationViewModel: TranslationViewModel,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    // Add these lines to extract time values from currentDate
    val currentYear = remember { currentDate.year + 1900 } // Java Date year is years since 1900
    val currentMonth = remember { currentDate.month + 1 } // Java Date month is 0-based
    val currentDay = remember { currentDate.date }

    val ttsHelper = remember { TextToSpeechHelper(context) }

    DisposableEffect(Unit) {
        onDispose {
            ttsHelper.shutdown()
        }
    }

    var expandedProductId by remember { mutableStateOf<Int?>(null) }
    var searchQuery by remember { mutableStateOf("") }
    var isSearching by remember { mutableStateOf(false) }
    var sortingMenuExpanded by remember { mutableStateOf(false) }
    var currentSortOption by remember { mutableStateOf(SortOption.EXPIRY_NEAREST) }
    var showOnlyExpired by remember { mutableStateOf(false) }
    var showOnlyCritical by remember { mutableStateOf(false) }

    // Filter and sort products based on search query and sort option
    val filteredProducts = remember(products, searchQuery, showOnlyExpired, showOnlyCritical, currentDate) {
        products.filter { product ->
            // Apply search filter
            val matchesSearch = if (searchQuery.isNotEmpty()) {
                product.productName.contains(searchQuery, ignoreCase = true) ||
                product.barcode.contains(searchQuery, ignoreCase = true) ||
                product.productType.contains(searchQuery, ignoreCase = true) ||
                product.branchName.contains(searchQuery, ignoreCase = true)
            } else true

            // Apply expired filter
            val matchesExpired = if (showOnlyExpired) {
                product.isExpired
            } else true

            // Apply critical filter (less than 30 days to expiry)
            val matchesCritical = if (showOnlyCritical) {
                val daysUntilExpiry = ((product.expireDate - currentDate.time) / (1000 * 60 * 60 * 24)).toInt()
                daysUntilExpiry <= 30 && !product.isExpired
            } else true

            matchesSearch && matchesExpired && matchesCritical
        }
    }

    // Sort the filtered products
    val sortedProducts = remember(filteredProducts, currentSortOption, currentDate) {
        when (currentSortOption) {
            SortOption.NAME_ASC -> filteredProducts.sortedBy { it.productName }
            SortOption.NAME_DESC -> filteredProducts.sortedByDescending { it.productName }
            SortOption.EXPIRY_NEAREST -> filteredProducts.sortedBy { it.expireDate }
            SortOption.EXPIRY_FARTHEST -> filteredProducts.sortedByDescending { it.expireDate }
            SortOption.QUANTITY_HIGH -> filteredProducts.sortedByDescending { it.currentQuantity }
            SortOption.QUANTITY_LOW -> filteredProducts.sortedBy { it.currentQuantity }
            SortOption.BRANCH_ASC -> filteredProducts.sortedBy { it.branchName }
            SortOption.BRANCH_DESC -> filteredProducts.sortedByDescending { it.branchName }
        }
    }

    // Create a LazyListState to control scrolling
    val listState = rememberLazyListState()

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            SearchBar(
                searchQuery = searchQuery,
                onSearchQueryChange = { newQuery ->
                    searchQuery = newQuery
                    isSearching = true
                    MainScope().launch {
                        delay(300)
                        isSearching = false
                    }
                },
                modifier = Modifier.weight(1f)
            )

            Spacer(modifier = Modifier.width(8.dp))

            Box {
                IconButton(onClick = { sortingMenuExpanded = true }) {
                    Icon(
                        Icons.Default.Sort,
                        contentDescription = "Sort",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }

                DropdownMenu(
                    expanded = sortingMenuExpanded,
                    onDismissRequest = { sortingMenuExpanded = false }
                ) {
                    SortOption.values().forEach { option ->
                        DropdownMenuItem(
                            text = { Text(stringResource(option.displayNameRes)) },
                            onClick = {
                                // Only update if the option is different
                                if (currentSortOption != option) {
                                    currentSortOption = option
                                    // Reset scroll position to top
                                    scope.launch {
                                        listState.animateScrollToItem(0)
                                    }
                                    // Close any expanded items
                                    expandedProductId = null
                                }
                                sortingMenuExpanded = false
                            },
                            leadingIcon = {
                                if (currentSortOption == option) {
                                    Icon(
                                        Icons.Default.Check,
                                        contentDescription = "Selected",
                                        tint = MaterialTheme.colorScheme.primary
                                    )
                                }
                            }
                        )
                    }
                }
            }
        }

        Text(
            text = stringResource(
                R.string.total_active_products,
                sortedProducts.size
            ),
            style = MaterialTheme.typography.bodySmall,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        when {
            isSearching -> {
                LoadingIndicator()
            }
            sortedProducts.isEmpty() -> {
                EmptyStateMessage(searchQuery)
            }
            else -> {
                LazyColumn(
                    state = listState,
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(
                        items = sortedProducts,
                        key = { it.id }
                    ) { product ->
                        ProductCard(
                            product = product,
                            isExpanded = expandedProductId == product.id,
                            onExpandClick = {
                                expandedProductId = if (expandedProductId == product.id) null else product.id
                            },
                            database = database,
                            currentDate = currentDate,
                            ttsHelper = ttsHelper,
                            translationViewModel = translationViewModel
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun LoadingIndicator() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator()
    }
}

@Composable
private fun EmptyStateMessage(searchQuery: String) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (searchQuery.isNotEmpty()) {
            Text(
                text = stringResource(R.string.no_results_for, searchQuery),
                style = MaterialTheme.typography.bodyLarge
            )
            Text(
                text = stringResource(R.string.try_different_search),
                style = MaterialTheme.typography.bodyMedium
            )
        } else {
            Text(
                text = stringResource(R.string.no_products_found),
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ProductCard(
    product: Product,
    isExpanded: Boolean,
    onExpandClick: () -> Unit,
    database: AppDatabase,
    currentDate: Date,
    ttsHelper: TextToSpeechHelper,
    translationViewModel: TranslationViewModel,
    modifier: Modifier = Modifier
) {
    val dateFormat = remember { SimpleDateFormat("dd/MM/yyyy", Locale.getDefault()) }
    val daysUntilExpiry = ((product.expireDate - currentDate.time) / (1000 * 60 * 60 * 24)).toInt()
    val scope = rememberCoroutineScope()
    var tempQuantity by remember(product.id) { mutableStateOf(product.currentQuantity.toString()) }
    var showError by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }
    var showDeleteConfirmation by remember { mutableStateOf(false) }

    ElevatedCard(
        modifier = modifier.fillMaxWidth(),
        onClick = onExpandClick
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Product Name and Barcode Column
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    TranslatableText(
                        text = product.productName,
                        translationViewModel = translationViewModel,
                        style = MaterialTheme.typography.titleMedium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    Text(
                        text = "Barcode: ${product.barcode}",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                // Action Buttons Row
                Row(
                    horizontalArrangement = Arrangement.End,
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(start = 8.dp)
                ) {
                    // TTS Button
                    IconButton(
                        onClick = { ttsHelper.speak(product.productName) },
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.VolumeUp,
                            contentDescription = "Speak product name",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }

                    // Expand/Collapse Button
                    IconButton(
                        onClick = onExpandClick,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = if (isExpanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                            contentDescription = if (isExpanded) "Show less" else "Show more"
                        )
                    }
                }
            }

            Text(
                text = "$daysUntilExpiry days until expiry",
                style = MaterialTheme.typography.bodyMedium,
                color = when {
                    daysUntilExpiry <= 30 -> Color(0xFFFF6B6B)  // Red for less than 1 month
                    daysUntilExpiry <= 90 -> Color(0xFFFFB84D)  // Yellow for less than 3 months
                    else -> Color(0xFF4CAF50)  // Green for more than 3 months
                }
            )

            if (isExpanded) {
                Spacer(modifier = Modifier.height(8.dp))
                Divider()
                Spacer(modifier = Modifier.height(8.dp))
                Column(
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    DetailRow("Branch", product.branchName)
                    DetailRow("Product Type", product.productType)
                    DetailRow("Batch", product.batch)
                    DetailRow("Current Quantity", product.currentQuantity.toString())
                    DetailRow("Initial Quantity", product.initialQuantity.toString())
                    DetailRow("Last Adjusted Quantity", tempQuantity)
                    DetailRow("Mfg Date", dateFormat.format(Date(product.mfgDate)))
                    DetailRow("Exp Date", dateFormat.format(Date(product.expireDate)))

                    // Quantity adjustment section
                    Spacer(modifier = Modifier.height(8.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        IconButton(
                            onClick = {
                                val current = tempQuantity.toIntOrNull() ?: 0
                                if (current > 0) {
                                    tempQuantity = (current - 1).toString()
                                }
                            }
                        ) {
                            Icon(Icons.Default.Remove, "Decrease quantity")
                        }

                        OutlinedTextField(
                            value = tempQuantity,
                            onValueChange = { newValue ->
                                if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                                    tempQuantity = newValue
                                }
                            },
                            label = { Text("Current Quantity") },
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                            modifier = Modifier.weight(1f).padding(horizontal = 8.dp),
                            singleLine = true
                        )

                        IconButton(
                            onClick = {
                                val current = tempQuantity.toIntOrNull() ?: 0
                                tempQuantity = (current + 1).toString()
                            }
                        ) {
                            Icon(Icons.Default.Add, "Increase quantity")
                        }

                        Button(
                            onClick = {
                                val newQuantity = tempQuantity.toIntOrNull()
                                when {
                                    newQuantity == null -> {
                                        showError = true
                                        errorMessage = "Invalid quantity"
                                    }
                                    newQuantity < 0 -> {
                                        showError = true
                                        errorMessage = "Quantity cannot be negative"
                                    }
                                    newQuantity > product.initialQuantity -> {
                                        showError = true
                                        errorMessage = "Cannot exceed initial quantity"
                                    }
                                    newQuantity == 0 -> {
                                        // Show confirmation dialog instead of deleting immediately
                                        showDeleteConfirmation = true
                                    }
                                    else -> {
                                        scope.launch {
                                            try {
                                                val updatedProduct = product.copy(currentQuantity = newQuantity)
                                                database.productDao().updateProduct(updatedProduct)
                                            } catch (e: Exception) {
                                                showError = true
                                                errorMessage = "Failed to update quantity"
                                                Log.e("ProductCard", "Error updating quantity", e)
                                            }
                                        }
                                    }
                                }
                            }
                        ) {
                            Icon(Icons.Default.Check, "Save")
                        }
                    }
                }
            }
        }

        // Error dialog
        if (showError) {
            AlertDialog(
                onDismissRequest = { showError = false },
                title = { Text("Error") },
                text = { Text(errorMessage) },
                confirmButton = {
                    TextButton(onClick = { showError = false }) {
                        Text("OK")
                    }
                }
            )
        }

        // Delete confirmation dialog
        if (showDeleteConfirmation) {
            AlertDialog(
                onDismissRequest = { showDeleteConfirmation = false },
                title = {
                    Text(
                        "Confirm Delete",
                        style = MaterialTheme.typography.titleLarge,
                        color = MaterialTheme.colorScheme.error
                    )
                },
                text = {
                    Column {
                        Text("Are you sure you want to delete this product?")
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = product.productName,
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = "${product.productType} - ${product.barcode}",
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                },
                confirmButton = {
                    Button(
                        onClick = {
                            scope.launch {
                                try {
                                    // Remove the product from database
                                    database.productDao().deleteProduct(product)
                                    showDeleteConfirmation = false
                                } catch (e: Exception) {
                                    showError = true
                                    errorMessage = "Failed to remove product"
                                    Log.e("ProductCard", "Error removing product", e)
                                    showDeleteConfirmation = false
                                }
                            }
                        },
                        colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.error)
                    ) {
                        Text("Delete")
                    }
                },
                dismissButton = {
                    OutlinedButton(
                        onClick = { showDeleteConfirmation = false }
                    ) {
                        Text("Cancel")
                    }
                }
            )
        }
    }
}

@Composable
private fun DetailRow(
    label: String,
    value: String
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

@Composable
private fun SearchBar(
    searchQuery: String,
    onSearchQueryChange: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    // Set up barcode scanner
    val barcodeScanner = remember {
        BarcodeScanner(context, scope).apply {
            setCallbacks(
                onProductFound = { _, _, code ->
                    // Just use the barcode for search
                    onSearchQueryChange(code)
                },
                onProductNotFound = { scannedBarcode ->
                    // Use the scanned barcode for search
                    onSearchQueryChange(scannedBarcode)
                },
                onScanError = { error ->
                    // Show error in log
                    Log.e("AllItemsScreen", "Barcode scan error: $error")
                }
            )
        }
    }

    // Set up barcode scanner launcher
    val barcodeLauncher = rememberLauncherForActivityResult(ScanContract()) { result ->
        result.contents?.let { scannedBarcode ->
            // Use the scanned barcode directly for search
            onSearchQueryChange(scannedBarcode)
        }
    }

    OutlinedTextField(
        value = searchQuery,
        onValueChange = onSearchQueryChange,
        modifier = modifier.fillMaxWidth(),
        placeholder = { Text(stringResource(R.string.search_products)) },
        leadingIcon = {
            Icon(
                Icons.Default.Search,
                contentDescription = stringResource(R.string.search)
            )
        },
        trailingIcon = {
            IconButton(
                onClick = {
                    val options = barcodeScanner.getScanOptions()
                    barcodeLauncher.launch(options)
                }
            ) {
                Icon(
                    imageVector = Icons.Default.QrCodeScanner,
                    contentDescription = "Scan Barcode",
                    tint = MaterialTheme.colorScheme.primary
                )
            }
        },
        singleLine = true
    )
}

enum class SortOption(@StringRes val displayNameRes: Int) {
    NAME_ASC(R.string.sort_name_asc),
    NAME_DESC(R.string.sort_name_desc),
    EXPIRY_NEAREST(R.string.sort_expiry_nearest),
    EXPIRY_FARTHEST(R.string.sort_expiry_farthest),
    QUANTITY_HIGH(R.string.sort_quantity_high),
    QUANTITY_LOW(R.string.sort_quantity_low),
    BRANCH_ASC(R.string.sort_branch_asc),
    BRANCH_DESC(R.string.sort_branch_desc)
}

    // Helper functions for date comparison are no longer needed as we're comparing Long values directly


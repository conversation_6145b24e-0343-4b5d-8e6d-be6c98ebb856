package rt.tt.temp.data

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import android.util.Log

class ProductCatalogImporter(private val productCatalogDao: ProductCatalogDao) {
    suspend fun importProductCatalog() {
        withContext(Dispatchers.IO) {
            try {
                // Clear existing data
                productCatalogDao.deleteAll()
                
                // Get products from ProductCatalogData
                val products = ProductCatalogData.getAllProducts()
                Log.d("ProductImporter", "Total products to import: ${products.size}")
                
                // Insert all products
                productCatalogDao.insertAll(products)
                
                // Verify import
                val count = productCatalogDao.getProductCount()
                Log.d("ProductImporter", "Products in database after import: $count")
                
                // Test specific barcode
                val testProduct = productCatalogDao.getProductByBarcode("6281064110025")
                Log.d("ProductImporter", "Test product 6281064110025: $testProduct")
            } catch (e: Exception) {
                Log.e("ProductImporter", "Import failed", e)
                throw e
            }
        }
    }
}

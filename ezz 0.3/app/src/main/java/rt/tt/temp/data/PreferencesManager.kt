package rt.tt.temp.data

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.*
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "settings")

class PreferencesManager(private val context: Context) {
    companion object {
        val DARK_MODE = booleanPreferencesKey("dark_mode")
        val ENABLE_NOTIFICATIONS = booleanPreferencesKey("enable_notifications")
        val NOTIFY_THREE_MONTHS = booleanPreferencesKey("notify_three_months")
        val NOTIFY_QUANTITY_ZERO = booleanPreferencesKey("notify_quantity_zero")
        val APP_LANGUAGE = stringPreferencesKey("app_language")
        val BACKUP_FREQUENCY = intPreferencesKey("backup_frequency_hours")
        private val NOTIFY_LOW_STOCK = booleanPreferencesKey("notify_low_stock")
    }

    val dataStore = context.dataStore

    suspend fun updateDarkMode(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[DARK_MODE] = enabled
        }
    }

    suspend fun updateNotificationsEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[ENABLE_NOTIFICATIONS] = enabled
        }
    }

    suspend fun updateThreeMonthsNotification(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[NOTIFY_THREE_MONTHS] = enabled
        }
    }

    suspend fun updateQuantityZeroNotification(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[NOTIFY_QUANTITY_ZERO] = enabled
        }
    }

    suspend fun updateLowStockNotification(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[NOTIFY_LOW_STOCK] = enabled
        }
    }

    val darkModeFlow: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[DARK_MODE] ?: false
    }

    val notificationsEnabledFlow: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[ENABLE_NOTIFICATIONS] ?: true
    }

    val threeMonthsNotificationFlow: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[NOTIFY_THREE_MONTHS] ?: true
    }

    val quantityZeroNotificationFlow: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[NOTIFY_QUANTITY_ZERO] ?: true
    }

    val lowStockNotificationFlow: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[NOTIFY_LOW_STOCK] ?: true
    }
}
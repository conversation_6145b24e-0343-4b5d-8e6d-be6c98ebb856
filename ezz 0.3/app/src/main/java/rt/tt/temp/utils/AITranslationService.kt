package rt.tt.temp.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Service for AI-based translation using local product name database
 * and ML Kit for other text
 */
class AITranslationService(private val context: Context) {
    private val TAG = "AITranslationService"
    private val mlKitTranslator = TranslationManager(context)

    /**
     * Translate text using AI-based approach
     * First checks if the text is a product name in our database,
     * if not, falls back to ML Kit translation
     *
     * @param text The text to translate
     * @param sourceLanguage The source language code
     * @param targetLanguage The target language code
     * @return The translated text
     */
    suspend fun translate(
        text: String,
        sourceLanguage: String,
        targetLanguage: String
    ): String = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Translating with AI: '$text' from $sourceLanguage to $targetLanguage")

            // If translating from Arabic to English
            if (sourceLanguage == "ar" && targetLanguage == "en") {
                // Check if it's a product name in our database
                if (TranslatedProductNames.hasTranslation(text)) {
                    val translation = TranslatedProductNames.getEnglishName(text)
                    Log.d(TAG, "Found in product database: '$text' -> '$translation'")
                    return@withContext translation
                }

                // Try to find partial matches for product names
                val partialMatch = findPartialProductMatch(text)
                if (partialMatch != null) {
                    Log.d(TAG, "Found partial match in product database: '$text' -> '$partialMatch'")
                    return@withContext partialMatch
                }

                // Try to find similar product names using fuzzy matching
                val fuzzyMatch = findFuzzyProductMatch(text)
                if (fuzzyMatch != null) {
                    Log.d(TAG, "Found fuzzy match in product database: '$text' -> '$fuzzyMatch'")
                    return@withContext fuzzyMatch
                }
            }

            // If translating from English to Arabic and we have a reverse mapping
            if (sourceLanguage == "en" && targetLanguage == "ar") {
                // Try to find the English text in our values
                val arabicName = findArabicForEnglish(text)
                if (arabicName != null) {
                    Log.d(TAG, "Found reverse mapping: '$text' -> '$arabicName'")
                    return@withContext arabicName
                }

                // Try fuzzy matching for English to Arabic
                val fuzzyArabicMatch = findFuzzyEnglishMatch(text)
                if (fuzzyArabicMatch != null) {
                    Log.d(TAG, "Found fuzzy English match: '$text' -> '$fuzzyArabicMatch'")
                    return@withContext fuzzyArabicMatch
                }
            }

            // Fall back to ML Kit translation
            Log.d(TAG, "Using ML Kit for translation")
            val mlKitResult = mlKitTranslator.translate(
                text = text,
                sourceLanguage = sourceLanguage,
                targetLanguage = targetLanguage
            )

            return@withContext mlKitResult
        } catch (e: Exception) {
            Log.e(TAG, "AI translation failed: ${e.message}", e)
            return@withContext text
        }
    }

    /**
     * Find a partial match for a product name
     * Useful for cases where the product name might have additional text
     */
    private fun findPartialProductMatch(text: String): String? {
        val allTranslations = TranslatedProductNames.getAllTranslations()

        // Try to find a product name that is contained within the text
        for ((arabicName, englishName) in allTranslations) {
            if (text.contains(arabicName)) {
                // Replace the Arabic part with the English translation
                return text.replace(arabicName, englishName)
            }
        }

        return null
    }

    /**
     * Find a fuzzy match for a product name
     * This is a simple implementation without using external libraries
     */
    private fun findFuzzyProductMatch(text: String): String? {
        val allTranslations = TranslatedProductNames.getAllTranslations()
        var bestMatch: Pair<String, Int>? = null

        // Split the input text into words
        val inputWords = text.split(" ", "،", ",")

        for ((arabicName, englishName) in allTranslations) {
            // Split the product name into words
            val productWords = arabicName.split(" ", "،", ",")

            // Count how many words match
            var matchCount = 0
            for (inputWord in inputWords) {
                if (inputWord.length < 3) continue // Skip very short words

                for (productWord in productWords) {
                    if (productWord.length < 3) continue // Skip very short words

                    if (inputWord == productWord ||
                        inputWord.contains(productWord) ||
                        productWord.contains(inputWord)) {
                        matchCount++
                        break
                    }
                }
            }

            // If we found a better match, update bestMatch
            if (matchCount > 0 && (bestMatch == null || matchCount > bestMatch.second)) {
                bestMatch = Pair(englishName, matchCount)
            }
        }

        return bestMatch?.first
    }

    /**
     * Find the Arabic name for an English translation
     * This is for reverse translation (English to Arabic)
     */
    private fun findArabicForEnglish(englishText: String): String? {
        val allTranslations = TranslatedProductNames.getAllTranslations()

        // Look for the English text in our values
        for ((arabicName, englishName) in allTranslations) {
            if (englishName.equals(englishText, ignoreCase = true)) {
                return arabicName
            }
        }

        return null
    }

    /**
     * Find a fuzzy match for an English product name
     * This is for reverse translation (English to Arabic)
     */
    private fun findFuzzyEnglishMatch(englishText: String): String? {
        val allTranslations = TranslatedProductNames.getAllTranslations()
        var bestMatch: Pair<String, Int>? = null

        // Split the input text into words
        val inputWords = englishText.toLowerCase().split(" ", ",")

        for ((arabicName, englishName) in allTranslations) {
            // Split the English name into words
            val englishWords = englishName.toLowerCase().split(" ", ",")

            // Count how many words match
            var matchCount = 0
            for (inputWord in inputWords) {
                if (inputWord.length < 3) continue // Skip very short words

                for (englishWord in englishWords) {
                    if (englishWord.length < 3) continue // Skip very short words

                    if (inputWord == englishWord ||
                        inputWord.contains(englishWord) ||
                        englishWord.contains(inputWord)) {
                        matchCount++
                        break
                    }
                }
            }

            // If we found a better match, update bestMatch
            if (matchCount > 0 && (bestMatch == null || matchCount > bestMatch.second)) {
                bestMatch = Pair(arabicName, matchCount)
            }
        }

        return bestMatch?.first
    }
}

package rt.tt.temp.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import rt.tt.temp.ui.viewmodels.TranslationProvider
import rt.tt.temp.ui.viewmodels.TranslationViewModel

/**
 * Dialog for selecting a translation provider
 */
@Composable
fun TranslationProviderDialog(
    translationViewModel: TranslationViewModel,
    onDismiss: () -> Unit
) {
    // Get current provider and create a temporary selection state
    val currentProvider by translationViewModel.translationProvider.collectAsState()
    var selectedProvider by remember { mutableStateOf(currentProvider) }
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Select Translation Provider",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.height(16.dp))

                // ML Kit option
                ProviderOption(
                    provider = TranslationProvider.ML_KIT,
                    isSelected = selectedProvider == TranslationProvider.ML_KIT,
                    onClick = { selectedProvider = TranslationProvider.ML_KIT }
                )

                Spacer(modifier = Modifier.height(8.dp))

                // AI Translate option
                ProviderOption(
                    provider = TranslationProvider.AI_TRANSLATE,
                    isSelected = selectedProvider == TranslationProvider.AI_TRANSLATE,
                    onClick = { selectedProvider = TranslationProvider.AI_TRANSLATE }
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Libre Translator option
                ProviderOption(
                    provider = TranslationProvider.LIBRE_TRANSLATOR,
                    isSelected = selectedProvider == TranslationProvider.LIBRE_TRANSLATOR,
                    onClick = { selectedProvider = TranslationProvider.LIBRE_TRANSLATOR }
                )

                Spacer(modifier = Modifier.height(8.dp))

                // DeepL option
                ProviderOption(
                    provider = TranslationProvider.DEEPL,
                    isSelected = selectedProvider == TranslationProvider.DEEPL,
                    onClick = { selectedProvider = TranslationProvider.DEEPL }
                )

                Spacer(modifier = Modifier.height(16.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    // Cancel button
                    TextButton(onClick = onDismiss) {
                        Text("Cancel")
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    // Confirm button
                    Button(
                        onClick = {
                            translationViewModel.setTranslationProvider(selectedProvider)
                            onDismiss()
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.primary
                        )
                    ) {
                        Text("Confirm")
                    }
                }
            }
        }
    }
}

@Composable
private fun ProviderOption(
    provider: TranslationProvider,
    isSelected: Boolean,
    onClick: () -> Unit
) {

    Button(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth(),
        colors = ButtonDefaults.buttonColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primary
            } else {
                MaterialTheme.colorScheme.primaryContainer
            },
            contentColor = if (isSelected) {
                MaterialTheme.colorScheme.onPrimary
            } else {
                MaterialTheme.colorScheme.onPrimaryContainer
            }
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = provider.displayName,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Bold
                )

                if (isSelected) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "Selected",
                        tint = if (isSelected) {
                            MaterialTheme.colorScheme.onPrimary
                        } else {
                            MaterialTheme.colorScheme.onPrimaryContainer
                        }
                    )
                }
            }

            Text(
                text = provider.description,
                style = MaterialTheme.typography.bodyMedium,
                color = if (isSelected) {
                    MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.8f)
                } else {
                    MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
                }
            )
        }
    }
}

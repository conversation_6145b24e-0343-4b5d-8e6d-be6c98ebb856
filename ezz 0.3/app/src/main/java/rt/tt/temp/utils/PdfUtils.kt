package rt.tt.temp.utils

import android.graphics.pdf.PdfDocument
import android.util.Log
import rt.tt.temp.data.Product
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.abs

object PdfUtils {
    private val DATE_FORMAT = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    private val HEADERS = listOf(
        "Category",
        "Product Name",
        "Product Type",
        "Branch",
        "Barcode",
        "Batch",
        "Initial Quantity",
        "Current Quantity",
        "Manufacturing Date",
        "Expiry Date",
        "Status",
        "Created At"
    )

    // Replace the expiry calculations with our helper function
    fun exportProductsToPDF(products: List<Product>, directory: File): File {
        if (!directory.exists()) {
            directory.mkdirs()
        }

        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
            .format(Date())
        val fileName = "inventory_export_$timestamp.pdf"
        val pdfFile = File(directory, fileName)
        val currentDate = Date()

        // Extract time values from currentDate
        val currentYear = currentDate.year + 1900 // Java Date year is years since 1900
        val currentMonth = currentDate.month + 1 // Java Date month is 0-based
        val currentDay = currentDate.date

        try {
            val document = PdfDocument()
            val pageInfo = PdfDocument.PageInfo.Builder(842, 595, 1).create() // A4 landscape

            // Create first page
            var currentPage = document.startPage(pageInfo)
            var canvas = currentPage.canvas

            // Set up text properties
            val paint = android.graphics.Paint().apply {
                textSize = 8f
                color = android.graphics.Color.BLACK
            }

            // Draw header
            canvas.drawText("Inventory Export - Page 1", 10f, 20f, paint)

            // Draw headers
            var y = 40f
            var x = 20f
            HEADERS.forEachIndexed { index, header ->
                canvas.drawText(header, x, y, paint)
                x += when (index) {
                    1, 2, 3 -> 100f // More space for names and types
                    4, 5 -> 80f     // Medium space for barcode and batch
                    else -> 60f     // Standard space for other columns
                }
            }

            // Draw products
            products.forEach { product ->
                y += 20f
                x = 20f

                val daysUntilExpiry = daysUntilExpiry(product, currentDate)

                val status = if (product.isExpired || daysUntilExpiry < 0) {
                    "Expired (${abs(daysUntilExpiry)} days ago)"
                } else {
                    "$daysUntilExpiry days until expiry"
                }

                val rowData = listOf(
                    if (product.isExpired) "Expired" else "Active",
                    product.productName,
                    product.productType,
                    product.branchName,
                    product.barcode,
                    product.batch,
                    product.initialQuantity.toString(),
                    product.currentQuantity.toString(),
                    DATE_FORMAT.format(product.mfgDate),
                    DATE_FORMAT.format(product.expireDate),
                    status,
                    DATE_FORMAT.format(Date(product.createdAt))
                )

                rowData.forEachIndexed { index, text ->
                    canvas.drawText(text, x, y, paint)
                    x += when (index) {
                        1, 2, 3 -> 100f
                        4, 5 -> 80f
                        else -> 60f
                    }
                }

                // Start new page if needed
                if (y > 550f) {
                    document.finishPage(currentPage)
                    currentPage = document.startPage(pageInfo)
                    canvas = currentPage.canvas
                    y = 40f
                }
            }

            // Add summary
            y += 40f
            canvas.drawText("Total Products: ${products.size}", 20f, y, paint)
            y += 20f
            canvas.drawText("Export Date: ${DATE_FORMAT.format(Date())}", 20f, y, paint)

            document.finishPage(currentPage)
            FileOutputStream(pdfFile).use { out ->
                document.writeTo(out)
            }
            document.close()

            Log.i("PdfUtils", "PDF export successful. File saved at: ${pdfFile.absolutePath}")
            return pdfFile

        } catch (e: Exception) {
            val errorMessage = "Failed to export PDF: ${e.message}"
            Log.e("PdfUtils", errorMessage, e)
            throw RuntimeException(errorMessage, e)
        }
    }

    // Add helper function for days until expiry calculation
    private fun daysUntilExpiry(product: Product, currentDate: Date): Int {
        return ((product.expireDate - currentDate.time) / (1000 * 60 * 60 * 24)).toInt()
    }
}
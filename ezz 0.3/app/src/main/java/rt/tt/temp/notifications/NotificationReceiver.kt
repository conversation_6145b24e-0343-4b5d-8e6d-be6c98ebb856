package rt.tt.temp.notifications

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent

class NotificationReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        val notificationService = NotificationService(context)
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED -> {
                // Reschedule notifications after device restart
                NotificationWorker.schedule(context)
            }
            "SHOW_DEMO_NOTIFICATIONS" -> {
                notificationService.showDemoNotifications()
            }
        }
    }
}
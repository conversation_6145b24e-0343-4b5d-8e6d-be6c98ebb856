package rt.tt.temp.ui.components

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Translate
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import rt.tt.temp.ui.viewmodels.TranslationProvider
import rt.tt.temp.ui.viewmodels.TranslationViewModel
import rt.tt.temp.utils.TranslationManager

/**
 * A composable that displays text with translation support
 *
 * @param text The original text to display
 * @param translationViewModel The translation view model
 * @param modifier Modifier for the Text composable
 * @param color Text color
 * @param style Text style
 * @param fontWeight Font weight
 * @param maxLines Maximum number of lines
 * @param overflow Text overflow handling
 * @param textAlign Text alignment
 */
@Composable
fun TranslatableText(
    text: String,
    translationViewModel: TranslationViewModel,
    modifier: Modifier = Modifier,
    color: Color = Color.Unspecified,
    style: TextStyle = LocalTextStyle.current,
    fontWeight: FontWeight? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Clip,
    textAlign: TextAlign? = null
) {
    // State to hold the displayed text (original or translated)
    var displayText by remember(text) { mutableStateOf(text) }

    // Get translation state, target language, and provider
    val isTranslationActive by translationViewModel.isTranslationActive.collectAsState()
    val targetLanguage by translationViewModel.targetLanguage.collectAsState()
    val translationProvider by translationViewModel.translationProvider.collectAsState()

    // Get Libre Translator state if it's the selected provider
    val libreTranslatorState by translationViewModel.libreTranslatorState.collectAsState()
    val isLibreTranslating = translationProvider == TranslationProvider.LIBRE_TRANSLATOR &&
                            libreTranslatorState == TranslationManager.DownloadState.DOWNLOADING

    // Track if we're currently translating
    var isTranslating by remember { mutableStateOf(false) }

    // Effect to update the displayed text when translation state or language changes
    LaunchedEffect(text, isTranslationActive, targetLanguage, translationProvider) {
        if (isTranslationActive) {
            isTranslating = true
            try {
                // Add a small delay to ensure the loading indicator is visible
                kotlinx.coroutines.delay(100)

                // Get the translated text
                val translated = translationViewModel.getTranslatedProductName(text)

                // Only update if the translation is different
                if (translated != text && translated.isNotBlank()) {
                    displayText = translated
                }
            } finally {
                // Add a small delay before hiding the loading indicator
                kotlinx.coroutines.delay(300)
                isTranslating = false
            }
        } else {
            displayText = text
        }
    }

    // Display the text with loading indicator if translating
    if (isTranslating || isLibreTranslating) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = displayText,
                modifier = modifier,
                color = color,
                style = style,
                fontWeight = fontWeight,
                maxLines = maxLines,
                overflow = overflow,
                textAlign = textAlign
            )
            Spacer(modifier = Modifier.width(4.dp))
            CircularProgressIndicator(
                modifier = Modifier.width(12.dp),
                strokeWidth = 2.dp,
                color = MaterialTheme.colorScheme.primary
            )
        }
    } else {
        Text(
            text = displayText,
            modifier = modifier,
            color = color,
            style = style,
            fontWeight = fontWeight,
            maxLines = maxLines,
            overflow = overflow,
            textAlign = textAlign
        )
    }
}

package rt.tt.temp.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.preference.PreferenceManager
import rt.tt.temp.notifications.NotificationService
import android.Manifest
import android.os.Build
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import kotlinx.coroutines.launch
import rt.tt.temp.data.PreferencesManager

@Composable
fun NotificationsScreen() {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val preferencesManager = remember { PreferencesManager(context) }
    val notificationService = remember { NotificationService(context) }
    
    var enableNotifications by remember { 
        mutableStateOf(false)
    }
    var notifyThreeMonths by remember { 
        mutableStateOf(false)
    }
    var notifyQuantityZero by remember { 
        mutableStateOf(false)
    }
    
    // Collect preferences flows
    LaunchedEffect(Unit) {
        preferencesManager.notificationsEnabledFlow.collect { enabled ->
            enableNotifications = enabled
        }
    }

    LaunchedEffect(Unit) {
        preferencesManager.threeMonthsNotificationFlow.collect { enabled ->
            notifyThreeMonths = enabled
        }
    }

    LaunchedEffect(Unit) {
        preferencesManager.quantityZeroNotificationFlow.collect { enabled ->
            notifyQuantityZero = enabled
        }
    }
    
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission(),
        onResult = { isGranted ->
            scope.launch {
                preferencesManager.updateNotificationsEnabled(isGranted)
                if (isGranted) {
                    notificationService.showDemoNotifications()
                }
            }
        }
    )

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header
        Text(
            text = "Notifications",
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.padding(bottom = 24.dp)
        )

        // Main Settings Card
        ElevatedCard(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .padding(16.dp)
                    .fillMaxWidth()
            ) {
                // Master Switch Section
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        modifier = Modifier.weight(1f),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Notifications,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.primary
                        )
                        Column {
                            Text(
                                text = "Enable Notifications",
                                style = MaterialTheme.typography.titleMedium
                            )
                            Text(
                                text = "Receive alerts about your products",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                    Switch(
                        checked = enableNotifications,
                        onCheckedChange = { checked ->
                            scope.launch {
                                preferencesManager.updateNotificationsEnabled(checked)
                            }
                        }
                    )
                }

                Divider(modifier = Modifier.padding(vertical = 8.dp))

                // Alert Types Section
                Text(
                    text = "Alert Types",
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.padding(vertical = 16.dp)
                )

                // Notification Preferences
                Column(
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Expiry Alert
                    NotificationPreferenceItem(
                        icon = Icons.Default.Timer,
                        title = "3-Month Expiry Alert",
                        subtitle = "Notify when products are approaching expiry",
                        checked = notifyThreeMonths && enableNotifications,
                        onCheckedChange = { checked ->
                            scope.launch {
                                preferencesManager.updateThreeMonthsNotification(checked)
                            }
                        },
                        enabled = enableNotifications
                    )

                    // Quantity Alert
                    NotificationPreferenceItem(
                        icon = Icons.Default.Inventory,
                        title = "Zero Quantity Alert",
                        subtitle = "Notify when product quantity reaches zero",
                        checked = notifyQuantityZero && enableNotifications,
                        onCheckedChange = { checked ->
                            scope.launch {
                                preferencesManager.updateQuantityZeroNotification(checked)
                            }
                        },
                        enabled = enableNotifications
                    )
                }

                // Test Section
                AnimatedVisibility(
                    visible = enableNotifications,
                    enter = fadeIn() + expandVertically(),
                    exit = fadeOut() + shrinkVertically()
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 24.dp)
                    ) {
                        Divider(modifier = Modifier.padding(bottom = 24.dp))
                        
                        Text(
                            text = "Testing",
                            style = MaterialTheme.typography.titleMedium,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )

                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            FilledTonalButton(
                                onClick = { 
                                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                                        permissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
                                    } else {
                                        notificationService.showDemoNotifications()
                                    }
                                },
                                modifier = Modifier.fillMaxWidth(),
                                enabled = enableNotifications
                            ) {
                                Icon(
                                    Icons.Default.Send,
                                    contentDescription = null,
                                    modifier = Modifier.padding(end = 8.dp)
                                )
                                Text("Send Test Notification")
                            }

                            Spacer(modifier = Modifier.height(8.dp))
                            
                            Text(
                                text = "Send a test notification to verify your settings",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.padding(horizontal = 16.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun NotificationPreferenceItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    subtitle: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    enabled: Boolean = true
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp)),
        color = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = if (enabled) 0.5f else 0.2f)
    ) {
        Row(
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                modifier = Modifier.weight(1f),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = if (enabled) 
                        MaterialTheme.colorScheme.primary 
                    else 
                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                )
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.bodyLarge,
                        color = if (enabled) 
                            MaterialTheme.colorScheme.onSurface 
                        else 
                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                    )
                    Text(
                        text = subtitle,
                        style = MaterialTheme.typography.bodyMedium,
                        color = if (enabled) 
                            MaterialTheme.colorScheme.onSurfaceVariant 
                        else 
                            MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.38f),
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            Switch(
                checked = checked,
                onCheckedChange = onCheckedChange,
                enabled = enabled,
                modifier = Modifier.padding(start = 8.dp)
            )
        }
    }
}
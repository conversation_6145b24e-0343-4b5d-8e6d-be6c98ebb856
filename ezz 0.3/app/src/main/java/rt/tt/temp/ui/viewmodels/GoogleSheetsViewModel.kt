package rt.tt.temp.ui.viewmodels

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import rt.tt.temp.data.AppDatabase
import rt.tt.temp.services.GoogleSheetsApiService
import android.util.Log
import java.text.SimpleDateFormat
import java.util.*

class GoogleSheetsViewModel(private val context: Context) : ViewModel() {
    private val sheetsService = GoogleSheetsApiService()
    private val database = AppDatabase.getInstance(context)
    private val tag = "GoogleSheetsViewModel"

    private val _isConnected = MutableStateFlow(false)
    val isConnected: StateFlow<Boolean> = _isConnected.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _lastSyncTime = MutableStateFlow<String?>(null)
    val lastSyncTime: StateFlow<String?> = _lastSyncTime.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _operationStatus = MutableStateFlow<String?>(null)
    val operationStatus: StateFlow<String?> = _operationStatus.asStateFlow()

    private val _productCount = MutableStateFlow(0)
    val productCount: StateFlow<Int> = _productCount.asStateFlow()

    init {
        // Load initial product count
        viewModelScope.launch {
            try {
                val products = database.productDao().getAllProducts()
                _productCount.value = products.size
            } catch (e: Exception) {
                Log.e(tag, "Failed to load initial product count", e)
            }
        }
    }

    fun testConnection() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "Testing connection to Google Sheets..."
            _errorMessage.value = null
            
            try {
                Log.d(tag, "Starting connection test")
                val success = sheetsService.testConnection()
                
                if (success) {
                    _isConnected.value = true
                    _operationStatus.value = "✅ Connected to Google Sheets successfully!"
                    Log.d(tag, "Connection test successful")
                } else {
                    _isConnected.value = false
                    _errorMessage.value = "❌ Failed to connect to Google Sheets. Please check your internet connection."
                    _operationStatus.value = null
                    Log.e(tag, "Connection test failed")
                }
            } catch (e: Exception) {
                Log.e(tag, "Connection test exception", e)
                _isConnected.value = false
                _errorMessage.value = "Connection error: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun exportToSheets() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "📤 Exporting inventory to Google Sheets..."
            _errorMessage.value = null
            
            try {
                Log.d(tag, "Starting export to sheets")
                val products = database.productDao().getAllProducts()
                Log.d(tag, "Found ${products.size} products to export")
                
                if (products.isEmpty()) {
                    _operationStatus.value = "⚠️ No products found to export"
                    return@launch
                }
                
                val success = sheetsService.syncAllProducts(products)
                
                if (success) {
                    val currentTime = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                        .format(Date())
                    _lastSyncTime.value = currentTime
                    _operationStatus.value = "✅ Successfully exported ${products.size} products to Google Sheets!"
                    Log.d(tag, "Export successful: ${products.size} products")
                } else {
                    _errorMessage.value = "❌ Export failed. Please try again."
                    _operationStatus.value = null
                    Log.e(tag, "Export failed")
                }
            } catch (e: Exception) {
                Log.e(tag, "Export exception", e)
                _errorMessage.value = "Export error: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun importFromSheets() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "📥 Importing data from Google Sheets..."
            _errorMessage.value = null
            
            try {
                Log.d(tag, "Starting import from sheets")
                val products = sheetsService.getAllProducts()
                Log.d(tag, "Retrieved ${products.size} products from sheets")
                
                if (products.isNotEmpty()) {
                    // Clear existing data and insert new data
                    database.productDao().deleteAll()
                    database.productDao().insertAll(products)
                    
                    // Update product count
                    _productCount.value = products.size
                    
                    val currentTime = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                        .format(Date())
                    _lastSyncTime.value = currentTime
                    _operationStatus.value = "✅ Successfully imported ${products.size} products from Google Sheets!"
                    Log.d(tag, "Import successful: ${products.size} products")
                } else {
                    _operationStatus.value = "⚠️ No products found in Google Sheets to import"
                    Log.w(tag, "No products found in sheets")
                }
            } catch (e: Exception) {
                Log.e(tag, "Import exception", e)
                _errorMessage.value = "Import error: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun syncBidirectional() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "🔄 Performing bidirectional sync..."
            _errorMessage.value = null
            
            try {
                Log.d(tag, "Starting bidirectional sync")
                
                // First export local data
                val localProducts = database.productDao().getAllProducts()
                val exportSuccess = sheetsService.syncAllProducts(localProducts)
                
                if (!exportSuccess) {
                    _errorMessage.value = "❌ Failed to export local data"
                    return@launch
                }
                
                // Then import any additional data from sheets
                val sheetProducts = sheetsService.getAllProducts()
                
                // For now, we'll just update the local count
                _productCount.value = localProducts.size
                
                val currentTime = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                    .format(Date())
                _lastSyncTime.value = currentTime
                _operationStatus.value = "✅ Bidirectional sync completed successfully!"
                Log.d(tag, "Bidirectional sync successful")
                
            } catch (e: Exception) {
                Log.e(tag, "Bidirectional sync exception", e)
                _errorMessage.value = "Sync error: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun clearError() {
        _errorMessage.value = null
    }

    fun clearStatus() {
        _operationStatus.value = null
    }

    fun disconnect() {
        _isConnected.value = false
        _lastSyncTime.value = null
        _operationStatus.value = "Disconnected from Google Sheets"
    }
}

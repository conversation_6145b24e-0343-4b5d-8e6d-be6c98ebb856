package rt.tt.temp.ui.viewmodels

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import rt.tt.temp.data.AppDatabase
import rt.tt.temp.services.GoogleSheetsApiService
import android.util.Log
import java.text.SimpleDateFormat
import java.util.*

class GoogleSheetsViewModel(private val context: Context) : ViewModel() {
    private val sheetsService = GoogleSheetsApiService()
    private val database = AppDatabase.getInstance(context)
    private val tag = "GoogleSheetsViewModel"

    private val _isConnected = MutableStateFlow(false)
    val isConnected: StateFlow<Boolean> = _isConnected.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _lastSyncTime = MutableStateFlow<String?>(null)
    val lastSyncTime: StateFlow<String?> = _lastSyncTime.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _operationStatus = MutableStateFlow<String?>(null)
    val operationStatus: StateFlow<String?> = _operationStatus.asStateFlow()

    private val _productCount = MutableStateFlow(0)
    val productCount: StateFlow<Int> = _productCount.asStateFlow()

    private val _availableBranches = MutableStateFlow<List<String>>(emptyList())
    val availableBranches: StateFlow<List<String>> = _availableBranches.asStateFlow()

    private val _selectedBranch = MutableStateFlow<String?>(null)
    val selectedBranch: StateFlow<String?> = _selectedBranch.asStateFlow()

    private val _branchProductCounts = MutableStateFlow<Map<String, Int>>(emptyMap())
    val branchProductCounts: StateFlow<Map<String, Int>> = _branchProductCounts.asStateFlow()

    init {
        // Load initial data
        viewModelScope.launch {
            try {
                val products = database.productDao().getAllProducts()
                _productCount.value = products.size

                // Load available branches from local data
                loadLocalBranches()
            } catch (e: Exception) {
                Log.e(tag, "Failed to load initial data", e)
            }
        }
    }

    private suspend fun loadLocalBranches() {
        try {
            val products = database.productDao().getAllProducts()
            val branches = products.map { it.branchName }.distinct().sorted()
            _availableBranches.value = branches

            // Calculate product counts per branch
            val branchCounts = branches.associateWith { branch ->
                products.count { it.branchName == branch }
            }
            _branchProductCounts.value = branchCounts

            Log.d(tag, "Loaded ${branches.size} branches: $branches")
        } catch (e: Exception) {
            Log.e(tag, "Failed to load local branches", e)
        }
    }

    fun testConnection() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "Testing connection to Google Sheets..."
            _errorMessage.value = null

            try {
                Log.d(tag, "Starting connection test")
                val success = sheetsService.testConnection()

                if (success) {
                    _isConnected.value = true
                    _operationStatus.value = "✅ Connected to Google Sheets successfully!"
                    Log.d(tag, "Connection test successful")
                } else {
                    _isConnected.value = false
                    _errorMessage.value = "❌ Failed to connect to Google Sheets. Please check your internet connection."
                    _operationStatus.value = null
                    Log.e(tag, "Connection test failed")
                }
            } catch (e: Exception) {
                Log.e(tag, "Connection test exception", e)
                _isConnected.value = false
                _errorMessage.value = "Connection error: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun exportToSheets() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "📤 Exporting inventory to Google Sheets..."
            _errorMessage.value = null

            try {
                Log.d(tag, "Starting export to sheets")
                val products = database.productDao().getAllProducts()
                Log.d(tag, "Found ${products.size} products to export")

                if (products.isEmpty()) {
                    _operationStatus.value = "⚠️ No products found to export"
                    return@launch
                }

                val success = sheetsService.syncAllProducts(products)

                if (success) {
                    val currentTime = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                        .format(Date())
                    _lastSyncTime.value = currentTime
                    _operationStatus.value = "✅ Successfully exported ${products.size} products to Google Sheets!"
                    Log.d(tag, "Export successful: ${products.size} products")
                } else {
                    _errorMessage.value = "❌ Export failed. Please try again."
                    _operationStatus.value = null
                    Log.e(tag, "Export failed")
                }
            } catch (e: Exception) {
                Log.e(tag, "Export exception", e)
                _errorMessage.value = "Export error: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun importFromSheets() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "📥 Importing data from Google Sheets..."
            _errorMessage.value = null

            try {
                Log.d(tag, "Starting import from sheets")
                val products = sheetsService.getAllProducts()
                Log.d(tag, "Retrieved ${products.size} products from sheets")

                if (products.isNotEmpty()) {
                    // Clear existing data and insert new data
                    database.productDao().deleteAll()
                    database.productDao().insertAll(products)

                    // Update product count
                    _productCount.value = products.size

                    val currentTime = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                        .format(Date())
                    _lastSyncTime.value = currentTime
                    _operationStatus.value = "✅ Successfully imported ${products.size} products from Google Sheets!"
                    Log.d(tag, "Import successful: ${products.size} products")
                } else {
                    _operationStatus.value = "⚠️ No products found in Google Sheets to import"
                    Log.w(tag, "No products found in sheets")
                }
            } catch (e: Exception) {
                Log.e(tag, "Import exception", e)
                _errorMessage.value = "Import error: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun syncBidirectional() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "🔄 Performing bidirectional sync..."
            _errorMessage.value = null

            try {
                Log.d(tag, "Starting bidirectional sync")

                // First export local data
                val localProducts = database.productDao().getAllProducts()
                val exportSuccess = sheetsService.syncAllProducts(localProducts)

                if (!exportSuccess) {
                    _errorMessage.value = "❌ Failed to export local data"
                    return@launch
                }

                // Then import any additional data from sheets
                val sheetProducts = sheetsService.getAllProducts()

                // For now, we'll just update the local count
                _productCount.value = localProducts.size

                val currentTime = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                    .format(Date())
                _lastSyncTime.value = currentTime
                _operationStatus.value = "✅ Bidirectional sync completed successfully!"
                Log.d(tag, "Bidirectional sync successful")

            } catch (e: Exception) {
                Log.e(tag, "Bidirectional sync exception", e)
                _errorMessage.value = "Sync error: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun clearError() {
        _errorMessage.value = null
    }

    fun clearStatus() {
        _operationStatus.value = null
    }

    fun disconnect() {
        _isConnected.value = false
        _lastSyncTime.value = null
        _selectedBranch.value = null
        _operationStatus.value = "Disconnected from Google Sheets"
    }

    // Branch-wise operations
    fun selectBranch(branchName: String?) {
        _selectedBranch.value = branchName
        Log.d(tag, "Selected branch: $branchName")
    }

    fun exportBranchToSheets(branchName: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "📤 Exporting $branchName data to Google Sheets..."
            _errorMessage.value = null

            try {
                Log.d(tag, "Starting export for branch: $branchName")
                val products = database.productDao().getAllProducts()
                val branchProducts = products.filter { it.branchName == branchName }

                Log.d(tag, "Found ${branchProducts.size} products for branch $branchName")

                if (branchProducts.isEmpty()) {
                    _operationStatus.value = "⚠️ No products found for branch $branchName"
                    return@launch
                }

                val success = sheetsService.syncProductsByBranch(branchName, branchProducts)

                if (success) {
                    val currentTime = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                        .format(Date())
                    _lastSyncTime.value = currentTime
                    _operationStatus.value = "✅ Successfully exported ${branchProducts.size} products from $branchName!"
                    Log.d(tag, "Branch export successful: $branchName - ${branchProducts.size} products")
                } else {
                    _errorMessage.value = "❌ Export failed for branch $branchName. Please try again."
                    _operationStatus.value = null
                    Log.e(tag, "Branch export failed: $branchName")
                }
            } catch (e: Exception) {
                Log.e(tag, "Branch export exception", e)
                _errorMessage.value = "Export error for $branchName: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun importBranchFromSheets(branchName: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "📥 Importing $branchName data from Google Sheets..."
            _errorMessage.value = null

            try {
                Log.d(tag, "Starting import for branch: $branchName")
                val branchProducts = sheetsService.getProductsByBranch(branchName)

                Log.d(tag, "Retrieved ${branchProducts.size} products for branch $branchName")

                if (branchProducts.isNotEmpty()) {
                    // Remove existing products for this branch
                    val existingProducts = database.productDao().getAllProducts()
                    val otherBranchProducts = existingProducts.filter { it.branchName != branchName }

                    // Clear all and insert other branches + new branch data
                    database.productDao().deleteAll()
                    database.productDao().insertAll(otherBranchProducts + branchProducts)

                    // Update counts
                    loadLocalBranches()

                    val currentTime = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                        .format(Date())
                    _lastSyncTime.value = currentTime
                    _operationStatus.value = "✅ Successfully imported ${branchProducts.size} products for $branchName!"
                    Log.d(tag, "Branch import successful: $branchName - ${branchProducts.size} products")
                } else {
                    _operationStatus.value = "⚠️ No products found for branch $branchName in Google Sheets"
                    Log.w(tag, "No products found for branch: $branchName")
                }
            } catch (e: Exception) {
                Log.e(tag, "Branch import exception", e)
                _errorMessage.value = "Import error for $branchName: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun syncAllBranchesToSheets() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "🔄 Syncing all branches to Google Sheets..."
            _errorMessage.value = null

            try {
                Log.d(tag, "Starting sync for all branches")
                val branches = _availableBranches.value
                var successCount = 0
                var totalProducts = 0

                for (branchName in branches) {
                    val products = database.productDao().getAllProducts()
                    val branchProducts = products.filter { it.branchName == branchName }

                    if (branchProducts.isNotEmpty()) {
                        val success = sheetsService.syncProductsByBranch(branchName, branchProducts)
                        if (success) {
                            successCount++
                            totalProducts += branchProducts.size
                            Log.d(tag, "Synced branch $branchName: ${branchProducts.size} products")
                        } else {
                            Log.e(tag, "Failed to sync branch: $branchName")
                        }
                    }
                }

                if (successCount == branches.size) {
                    val currentTime = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                        .format(Date())
                    _lastSyncTime.value = currentTime
                    _operationStatus.value = "✅ Successfully synced all $successCount branches ($totalProducts products)!"
                    Log.d(tag, "All branches sync successful: $successCount branches, $totalProducts products")
                } else {
                    _errorMessage.value = "⚠️ Synced $successCount of ${branches.size} branches. Some failed."
                    _operationStatus.value = null
                    Log.w(tag, "Partial sync: $successCount of ${branches.size} branches")
                }
            } catch (e: Exception) {
                Log.e(tag, "All branches sync exception", e)
                _errorMessage.value = "Sync error: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadBranchesFromSheets() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "📋 Loading branches from Google Sheets..."
            _errorMessage.value = null

            try {
                Log.d(tag, "Loading branches from sheets")
                val sheetBranches = sheetsService.getAllBranches()

                if (sheetBranches.isNotEmpty()) {
                    // Merge with local branches
                    val localBranches = _availableBranches.value
                    val allBranches = (localBranches + sheetBranches).distinct().sorted()
                    _availableBranches.value = allBranches

                    _operationStatus.value = "✅ Loaded ${sheetBranches.size} branches from Google Sheets"
                    Log.d(tag, "Loaded branches from sheets: $sheetBranches")
                } else {
                    _operationStatus.value = "⚠️ No branches found in Google Sheets"
                    Log.w(tag, "No branches found in sheets")
                }
            } catch (e: Exception) {
                Log.e(tag, "Load branches exception", e)
                _errorMessage.value = "Error loading branches: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }
}

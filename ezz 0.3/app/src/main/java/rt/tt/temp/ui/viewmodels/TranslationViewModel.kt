package rt.tt.temp.ui.viewmodels

import android.app.Application
import android.content.Context
import android.util.Log
import android.widget.Toast
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import androidx.preference.PreferenceManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import rt.tt.temp.utils.TranslationManager

enum class Language(val code: String, val displayName: String) {
    ENGLISH("en", "English"),
    BANGLA("bn", "বাংলা (Bangla)"),
    ARABIC("ar", "العربية (Arabic)")
}

/**
 * Enum representing different translation providers
 */
enum class TranslationProvider(val displayName: String, val description: String) {
    ML_KIT("ML Kit Enhanced", "Improved Bangla & spell correction (Active)"),
    AI_TRANSLATE("AI Translate", "Smart product name translation (Disabled)"),
    LIBRE_TRANSLATOR("Libre Translator", "Open-source translation service (Disabled)"),
    DEEPL("DeepL", "High-quality neural machine translation (Disabled)")
}

/**
 * ViewModel to manage translation state across the app
 */
class TranslationViewModel(application: Application) : AndroidViewModel(application) {

    // Shared Preferences
    private val prefs = PreferenceManager.getDefaultSharedPreferences(application.applicationContext)

    // Translation service
    private val translationManager = TranslationManager(application.applicationContext)
    // Note: Other translation services are disabled

    // Whether translation is currently active
    private val _isTranslationActive = MutableStateFlow(false)
    val isTranslationActive: StateFlow<Boolean> = _isTranslationActive.asStateFlow()

    // Selected target language
    private val _targetLanguage = MutableStateFlow(getSavedLanguage())
    val targetLanguage: StateFlow<Language> = _targetLanguage.asStateFlow()

    // Selected translation provider
    private val _translationProvider = MutableStateFlow(getSavedProvider())
    val translationProvider: StateFlow<TranslationProvider> = _translationProvider.asStateFlow()

    // Show language selection dialog
    private val _showLanguageDialog = MutableStateFlow(false)
    val showLanguageDialog: StateFlow<Boolean> = _showLanguageDialog.asStateFlow()

    // Show provider selection dialog
    private val _showProviderDialog = MutableStateFlow(false)
    val showProviderDialog: StateFlow<Boolean> = _showProviderDialog.asStateFlow()

    // Cache for translated product names
    private val translatedNames = mutableMapOf<String, String>()

    /**
     * Show language selection dialog
     */
    fun showLanguageDialog() {
        _showLanguageDialog.value = true
    }

    /**
     * Hide language selection dialog
     */
    fun hideLanguageDialog() {
        _showLanguageDialog.value = false
    }

    /**
     * Set the target language and activate translation
     */
    fun setLanguage(language: Language) {
        // Check if trying to set Bangla as target language
        if (language == Language.BANGLA) {
            // Show toast message that Bangla is locked
            Toast.makeText(
                getApplication(),
                "Bangla translation is currently locked",
                Toast.LENGTH_SHORT
            ).show()
            // Don't change the language, just close the dialog
            _showLanguageDialog.value = false
            return
        }

        if (_targetLanguage.value != language) {
            _targetLanguage.value = language
            // Save language preference
            prefs.edit().putString("translation_language", language.name).apply()
            translatedNames.clear() // Clear cache when language changes
        }
        _isTranslationActive.value = true
        _showLanguageDialog.value = false
    }

    /**
     * Get the saved language from preferences
     */
    private fun getSavedLanguage(): Language {
        val savedLanguage = prefs.getString("translation_language", Language.ENGLISH.name)
        return try {
            val language = Language.valueOf(savedLanguage ?: Language.ENGLISH.name)
            // If saved language is Bangla, return English instead (since Bangla is locked)
            if (language == Language.BANGLA) {
                Log.d("TranslationViewModel", "Bangla language is locked, defaulting to English")
                Language.ENGLISH
            } else {
                language
            }
        } catch (e: Exception) {
            Language.ENGLISH
        }
    }

    /**
     * Toggle translation state
     */
    fun toggleTranslation() {
        if (!_isTranslationActive.value) {
            showLanguageDialog()
        } else {
            _isTranslationActive.value = false
            translatedNames.clear()
        }
    }

    /**
     * Get translated product name
     * @param originalName The original product name
     * @return The translated name if translation is active, otherwise the original name
     */
    suspend fun getTranslatedProductName(originalName: String): String {
        // If translation is not active, return original name
        if (!_isTranslationActive.value) {
            return originalName
        }

        // If the text is empty or just whitespace, return it as is
        if (originalName.isBlank()) {
            return originalName
        }

        // Log the original text for debugging
        Log.d("TranslationViewModel", "Translating: '$originalName'")

        // Identify the language of the original text
        val sourceLanguage = translationManager.identifyLanguage(originalName)
        Log.d("TranslationViewModel", "Detected language: $sourceLanguage")

        // If the source language is the same as the target language, return the original text
        if (sourceLanguage == _targetLanguage.value.code) {
            Log.d("TranslationViewModel", "Source and target languages are the same, skipping translation")
            return originalName
        }

        // Create a cache key that includes provider, source and target languages
        val cacheKey = "${_translationProvider.value.name}:$sourceLanguage:${_targetLanguage.value.code}:$originalName"

        // Check if we already have a translation in the cache
        if (translatedNames.containsKey(cacheKey)) {
            val cachedTranslation = translatedNames[cacheKey] ?: originalName
            Log.d("TranslationViewModel", "Using cached translation: '$cachedTranslation'")
            return cachedTranslation
        }

        // If not in cache, translate and store in cache
        try {
            Log.d("TranslationViewModel", "Using provider: ${_translationProvider.value.name}")

            // Always use ML Kit for translation regardless of selected provider
            val translated = translationManager.translate(
                text = originalName,
                sourceLanguage = sourceLanguage,
                targetLanguage = _targetLanguage.value.code
            )

            // Log which provider was selected (for debugging)
            when (_translationProvider.value) {
                TranslationProvider.ML_KIT -> {
                    Log.d("TranslationViewModel", "Using ML Kit for translation")
                }
                TranslationProvider.AI_TRANSLATE -> {
                    Log.d("TranslationViewModel", "Selected AI Translate but using ML Kit (other providers disabled)")
                }
                TranslationProvider.LIBRE_TRANSLATOR -> {
                    Log.d("TranslationViewModel", "Selected Libre Translator but using ML Kit (other providers disabled)")
                }
                TranslationProvider.DEEPL -> {
                    Log.d("TranslationViewModel", "Selected DeepL but using ML Kit (other providers disabled)")
                }
            }

            // Check if translation was successful (not empty and different from original)
            if (translated.isNotBlank() && translated != originalName) {
                Log.d("TranslationViewModel", "Translation successful: '$translated'")
                // Store in cache and return the translated text
                translatedNames[cacheKey] = translated
                return translated
            } else {
                Log.w("TranslationViewModel", "Translation returned same text or empty result")
                return originalName
            }
        } catch (e: Exception) {
            Log.e("TranslationViewModel", "Translation failed: ${e.message}", e)
            // Return original name if translation fails
            return originalName
        }
    }

    /**
     * Download all language models
     * @return True if download was successful, false otherwise
     */
    suspend fun downloadAllLanguages(): Boolean {
        return translationManager.downloadAllLanguages()
    }

    /**
     * Get the download state for English
     */
    val englishDownloadState = translationManager.englishDownloadState

    /**
     * Get the download state for Bangla
     */
    val banglaDownloadState = translationManager.banglaDownloadState

    /**
     * Get the download state for Arabic
     */
    val arabicDownloadState = translationManager.arabicDownloadState

    /**
     * Get the state of Libre Translator
     */
    val libreTranslatorState = translationManager.libreTranslatorState

    /**
     * Check if a language is downloaded
     */
    suspend fun isLanguageDownloaded(language: Language): Boolean {
        return translationManager.isLanguageDownloaded(language)
    }

    /**
     * Show provider selection dialog
     */
    fun showProviderDialog() {
        _showProviderDialog.value = true
    }

    /**
     * Hide provider selection dialog
     */
    fun hideProviderDialog() {
        _showProviderDialog.value = false
    }

    /**
     * Set the translation provider
     */
    fun setTranslationProvider(provider: TranslationProvider) {
        // Only do something if the provider is changing
        if (_translationProvider.value != provider) {
            _translationProvider.value = provider

            // Save provider preference
            prefs.edit().putString("translation_provider", provider.name).apply()

            // Show appropriate toast message based on the selected provider
            when (provider) {
                TranslationProvider.ML_KIT -> {
                    Toast.makeText(
                        getApplication(),
                        "Using ML Kit with enhanced Bangla translation",
                        Toast.LENGTH_SHORT
                    ).show()
                }
                TranslationProvider.AI_TRANSLATE -> {
                    Toast.makeText(
                        getApplication(),
                        "AI Translate is currently disabled. Using ML Kit instead.",
                        Toast.LENGTH_SHORT
                    ).show()
                }
                TranslationProvider.LIBRE_TRANSLATOR -> {
                    Toast.makeText(
                        getApplication(),
                        "Libre Translator is currently disabled. Using ML Kit instead.",
                        Toast.LENGTH_SHORT
                    ).show()
                }
                TranslationProvider.DEEPL -> {
                    Toast.makeText(
                        getApplication(),
                        "DeepL is currently disabled. Using ML Kit instead.",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            // Clear translation cache when provider changes
            translatedNames.clear()
        }

        _showProviderDialog.value = false
    }

    /**
     * Get the saved provider from preferences
     */
    private fun getSavedProvider(): TranslationProvider {
        val savedProvider = prefs.getString("translation_provider", TranslationProvider.ML_KIT.name)
        return try {
            TranslationProvider.valueOf(savedProvider ?: TranslationProvider.ML_KIT.name)
        } catch (e: Exception) {
            TranslationProvider.ML_KIT
        }
    }

    /**
     * Clean up resources when ViewModel is cleared
     */
    override fun onCleared() {
        super.onCleared()
        translationManager.shutdown()
    }
}

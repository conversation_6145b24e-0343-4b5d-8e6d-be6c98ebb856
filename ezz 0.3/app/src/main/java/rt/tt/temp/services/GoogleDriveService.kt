package rt.tt.temp.services

import android.content.Context
import android.util.Log
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.common.api.Scope
import com.google.api.client.extensions.android.http.AndroidHttp
import com.google.api.client.googleapis.extensions.android.gms.auth.GoogleAccountCredential
import com.google.api.client.json.gson.GsonFactory
import com.google.api.client.http.javanet.NetHttpTransport
import com.google.api.services.drive.Drive
import com.google.api.services.drive.DriveScopes
import com.google.api.services.drive.model.File
import com.google.api.services.drive.model.FileList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.InputStream
import java.util.Collections

class GoogleDriveService(private val context: Context) {
    private var driveService: Drive? = null
    private val tag = "GoogleDriveService"

    companion object {
        const val APP_FOLDER_NAME = "EZZ Inventory"
        const val BACKUP_FOLDER_NAME = "Backups"
        const val EXPORTS_FOLDER_NAME = "Exports"
    }

    /**
     * Initialize the Google Drive service with the signed-in account
     */
    suspend fun initialize(): Boolean = withContext(Dispatchers.IO) {
        try {
            val account = GoogleSignIn.getLastSignedInAccount(context)
            if (account == null) {
                Log.e(tag, "No signed-in Google account found")
                return@withContext false
            }

            Log.d(tag, "Found signed-in account: ${account.email}")

            // Check if account has the required scopes
            val hasScope = GoogleSignIn.hasPermissions(account, Scope(DriveScopes.DRIVE_FILE))
            if (!hasScope) {
                Log.e(tag, "Account does not have required Drive scope")
                return@withContext false
            }

            val credential = GoogleAccountCredential.usingOAuth2(
                context,
                Collections.singleton(DriveScopes.DRIVE_FILE)
            )
            credential.selectedAccount = account.account

            Log.d(tag, "Creating Drive service builder...")

            // Try AndroidHttp first, fallback to NetHttpTransport if it fails
            val transport = try {
                AndroidHttp.newCompatibleTransport().also {
                    Log.d(tag, "AndroidHttp transport created")
                }
            } catch (e: Exception) {
                Log.w(tag, "AndroidHttp failed, using NetHttpTransport", e)
                NetHttpTransport()
            }

            val jsonFactory = GsonFactory()
            Log.d(tag, "JSON factory created")

            driveService = Drive.Builder(transport, jsonFactory, credential)
                .setApplicationName("EZZ Inventory")
                .build()

            Log.d(tag, "Drive service built successfully")

            Log.d(tag, "Google Drive service initialized successfully")

            // Test the connection with a simple API call
            try {
                val aboutRequest = driveService?.about()?.get()?.setFields("user")
                val about = aboutRequest?.execute()
                Log.d(tag, "Drive API test successful. User: ${about?.user?.displayName}")
            } catch (e: Exception) {
                Log.w(tag, "Drive API test failed, but service initialized", e)
            }

            true
        } catch (e: Exception) {
            Log.e(tag, "Failed to initialize Google Drive service", e)
            false
        }
    }

    /**
     * Check if the service is initialized and ready to use
     */
    fun isInitialized(): Boolean = driveService != null

    /**
     * Create the main app folder structure in Google Drive
     */
    suspend fun createAppFolderStructure(): Boolean = withContext(Dispatchers.IO) {
        try {
            val drive = driveService ?: return@withContext false

            // Create main app folder
            val appFolder = createFolder(APP_FOLDER_NAME, null)
            if (appFolder == null) {
                Log.e(tag, "Failed to create app folder")
                return@withContext false
            }

            // Create subfolders
            createFolder(BACKUP_FOLDER_NAME, appFolder.id)
            createFolder(EXPORTS_FOLDER_NAME, appFolder.id)

            Log.d(tag, "App folder structure created successfully")
            true
        } catch (e: Exception) {
            Log.e(tag, "Failed to create app folder structure", e)
            false
        }
    }

    /**
     * Create a folder in Google Drive
     */
    suspend fun createFolder(name: String, parentId: String?): File? = withContext(Dispatchers.IO) {
        try {
            val drive = driveService ?: return@withContext null

            // Check if folder already exists
            val existingFolder = findFileByName(name, parentId, "application/vnd.google-apps.folder")
            if (existingFolder != null) {
                Log.d(tag, "Folder '$name' already exists")
                return@withContext existingFolder
            }

            val fileMetadata = File().apply {
                this.name = name
                mimeType = "application/vnd.google-apps.folder"
                if (parentId != null) {
                    parents = listOf(parentId)
                }
            }

            val folder = drive.files().create(fileMetadata).execute()
            Log.d(tag, "Created folder: $name with ID: ${folder.id}")
            folder
        } catch (e: Exception) {
            Log.e(tag, "Failed to create folder: $name", e)
            null
        }
    }

    /**
     * Upload a file to Google Drive
     */
    suspend fun uploadFile(
        fileName: String,
        content: ByteArray,
        mimeType: String,
        parentFolderId: String? = null
    ): File? = withContext(Dispatchers.IO) {
        try {
            val drive = driveService ?: return@withContext null

            val fileMetadata = File().apply {
                name = fileName
                if (parentFolderId != null) {
                    parents = listOf(parentFolderId)
                }
            }

            val mediaContent = com.google.api.client.http.ByteArrayContent(mimeType, content)

            val file = drive.files().create(fileMetadata, mediaContent)
                .setFields("id, name, size, modifiedTime")
                .execute()

            Log.d(tag, "Uploaded file: $fileName with ID: ${file.id}")
            file
        } catch (e: Exception) {
            Log.e(tag, "Failed to upload file: $fileName", e)
            null
        }
    }

    /**
     * Download a file from Google Drive
     */
    suspend fun downloadFile(fileId: String): ByteArray? = withContext(Dispatchers.IO) {
        try {
            val drive = driveService ?: return@withContext null

            val outputStream = ByteArrayOutputStream()
            drive.files().get(fileId).executeMediaAndDownloadTo(outputStream)

            val content = outputStream.toByteArray()
            Log.d(tag, "Downloaded file with ID: $fileId, size: ${content.size} bytes")
            content
        } catch (e: Exception) {
            Log.e(tag, "Failed to download file with ID: $fileId", e)
            null
        }
    }

    /**
     * List files in a specific folder
     */
    suspend fun listFiles(parentFolderId: String? = null): List<DriveFile> = withContext(Dispatchers.IO) {
        try {
            val drive = driveService ?: return@withContext emptyList()

            val query = if (parentFolderId != null) {
                "'$parentFolderId' in parents and trashed=false"
            } else {
                "trashed=false"
            }

            val result: FileList = drive.files().list()
                .setQ(query)
                .setFields("files(id, name, mimeType, size, modifiedTime, parents)")
                .setOrderBy("modifiedTime desc")
                .execute()

            val files = result.files.map { file ->
                DriveFile(
                    id = file.id,
                    name = file.name,
                    mimeType = file.mimeType,
                    size = file.size?.toLong() ?: 0L,
                    modifiedTime = file.modifiedTime?.value ?: 0L,
                    isFolder = file.mimeType == "application/vnd.google-apps.folder"
                )
            }

            Log.d(tag, "Listed ${files.size} files in folder: $parentFolderId")
            files
        } catch (e: Exception) {
            Log.e(tag, "Failed to list files in folder: $parentFolderId", e)
            emptyList()
        }
    }

    /**
     * Delete a file from Google Drive
     */
    suspend fun deleteFile(fileId: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val drive = driveService ?: return@withContext false
            drive.files().delete(fileId).execute()
            Log.d(tag, "Deleted file with ID: $fileId")
            true
        } catch (e: Exception) {
            Log.e(tag, "Failed to delete file with ID: $fileId", e)
            false
        }
    }

    /**
     * Update/Replace a file in Google Drive
     */
    suspend fun updateFile(
        fileId: String,
        content: ByteArray,
        mimeType: String
    ): File? = withContext(Dispatchers.IO) {
        try {
            val drive = driveService ?: return@withContext null

            val mediaContent = com.google.api.client.http.ByteArrayContent(mimeType, content)

            val file = drive.files().update(fileId, null, mediaContent)
                .setFields("id, name, size, modifiedTime")
                .execute()

            Log.d(tag, "Updated file with ID: $fileId")
            file
        } catch (e: Exception) {
            Log.e(tag, "Failed to update file with ID: $fileId", e)
            null
        }
    }

    /**
     * Find a file by name in a specific folder
     */
    private suspend fun findFileByName(
        name: String,
        parentId: String?,
        mimeType: String? = null
    ): File? = withContext(Dispatchers.IO) {
        try {
            val drive = driveService ?: return@withContext null

            var query = "name='$name' and trashed=false"
            if (parentId != null) {
                query += " and '$parentId' in parents"
            }
            if (mimeType != null) {
                query += " and mimeType='$mimeType'"
            }

            val result = drive.files().list()
                .setQ(query)
                .setFields("files(id, name, mimeType, parents)")
                .execute()

            result.files.firstOrNull()
        } catch (e: Exception) {
            Log.e(tag, "Failed to find file: $name", e)
            null
        }
    }

    /**
     * Get the app folder ID
     */
    suspend fun getAppFolderId(): String? = withContext(Dispatchers.IO) {
        val appFolder = findFileByName(APP_FOLDER_NAME, null, "application/vnd.google-apps.folder")
        appFolder?.id
    }

    /**
     * Get the backup folder ID
     */
    suspend fun getBackupFolderId(): String? = withContext(Dispatchers.IO) {
        val appFolderId = getAppFolderId() ?: return@withContext null
        val backupFolder = findFileByName(BACKUP_FOLDER_NAME, appFolderId, "application/vnd.google-apps.folder")
        backupFolder?.id
    }

    /**
     * Get the exports folder ID
     */
    suspend fun getExportsFolderId(): String? = withContext(Dispatchers.IO) {
        val appFolderId = getAppFolderId() ?: return@withContext null
        val exportsFolder = findFileByName(EXPORTS_FOLDER_NAME, appFolderId, "application/vnd.google-apps.folder")
        exportsFolder?.id
    }

    /**
     * Backup inventory data to Google Drive
     */
    suspend fun backupInventoryData(jsonData: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val backupFolderId = getBackupFolderId() ?: run {
                Log.e(tag, "Backup folder not found")
                return@withContext false
            }

            val timestamp = java.text.SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.getDefault())
                .format(java.util.Date())
            val fileName = "inventory_backup_$timestamp.json"

            val result = uploadFile(
                fileName = fileName,
                content = jsonData.toByteArray(),
                mimeType = "application/json",
                parentFolderId = backupFolderId
            )

            if (result != null) {
                Log.d(tag, "Inventory backup successful: $fileName")
                true
            } else {
                Log.e(tag, "Failed to backup inventory data")
                false
            }
        } catch (e: Exception) {
            Log.e(tag, "Error during inventory backup", e)
            false
        }
    }

    /**
     * Get available backup files
     */
    suspend fun getBackupFiles(): List<DriveFile> = withContext(Dispatchers.IO) {
        val backupFolderId = getBackupFolderId() ?: return@withContext emptyList()
        listFiles(backupFolderId).filter { it.name.contains("inventory_backup") }
    }
}

/**
 * Data class representing a file in Google Drive
 */
data class DriveFile(
    val id: String,
    val name: String,
    val mimeType: String,
    val size: Long,
    val modifiedTime: Long,
    val isFolder: Boolean
)

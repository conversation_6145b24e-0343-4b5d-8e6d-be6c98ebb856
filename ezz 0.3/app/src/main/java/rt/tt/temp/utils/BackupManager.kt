package rt.tt.temp.utils

import android.content.Context
import android.util.Log
import androidx.work.*
import java.util.concurrent.TimeUnit
import rt.tt.temp.data.AppDatabase

class BackupManager(private val context: Context) {
    companion object {
        private const val BACKUP_WORK_NAME = "automatic_backup_work"
        private const val TAG = "BackupManager"
    }

    fun scheduleAutomaticBackup(intervalHours: Int = 24) {
        val constraints = Constraints.Builder()
            .setRequiresCharging(false)
            .setRequiresBatteryNotLow(true)
            .build()

        val backupWorkRequest = PeriodicWorkRequestBuilder<AutoBackupWorker>(
            intervalHours.toLong(), TimeUnit.HOURS,
            // Add flex interval to ensure work runs
            intervalHours.toLong() / 2, TimeUnit.HOURS
        )
            .setConstraints(constraints)
            .setBackoffCriteria(
                BackoffPolicy.LINEAR,
                WorkRequest.MIN_BACKOFF_MILLIS,  // Fixed: Using correct constant
                TimeUnit.MILLISECONDS
            )
            .build()

        WorkManager.getInstance(context).enqueueUniquePeriodicWork(
            BACKUP_WORK_NAME,
            ExistingPeriodicWorkPolicy.UPDATE,  // Changed from REPLACE to UPDATE
            backupWorkRequest
        )
        
        Log.d(TAG, "Scheduled automatic backup every $intervalHours hours")
    }

    fun cancelAutomaticBackup() {
        WorkManager.getInstance(context).cancelUniqueWork(BACKUP_WORK_NAME)
        Log.d(TAG, "Cancelled automatic backup")
    }
}

class AutoBackupWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {

    override suspend fun doWork(): Result {
        return try {
            val database = AppDatabase.getInstance(applicationContext)
            val backupFile = BackupUtils.createBackup(
                context = applicationContext,
                database = database,
                destinationFile = null
            )
            
            if (backupFile != null) {
                Log.d("AutoBackupWorker", "Automatic backup created successfully")
                Result.success()
            } else {
                Log.e("AutoBackupWorker", "Failed to create automatic backup")
                Result.retry()
            }
        } catch (e: Exception) {
            Log.e("AutoBackupWorker", "Error during automatic backup", e)
            Result.failure()
        }
    }
}
package rt.tt.temp.ui.screens

import android.Manifest
import android.app.Activity
import android.os.Build
import android.content.pm.PackageManager
import android.widget.Toast
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.outlined.*
import androidx.compose.material.icons.outlined.Download
import androidx.compose.material.icons.outlined.BugReport
import androidx.compose.material.icons.outlined.Code
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.preference.PreferenceManager
import rt.tt.temp.notifications.NotificationService
import rt.tt.temp.notifications.NotificationWorker
import rt.tt.temp.ui.theme.ThemeViewModel
import androidx.lifecycle.viewmodel.compose.viewModel
import rt.tt.temp.utils.BackupManager
import rt.tt.temp.ui.viewmodels.TranslationViewModel
import rt.tt.temp.ui.viewmodels.TranslationProvider
import rt.tt.temp.utils.TranslationManager
import rt.tt.temp.utils.TranslationTester
import kotlinx.coroutines.launch
import rt.tt.temp.utils.BackupUtils
import rt.tt.temp.ui.components.TranslationProviderDialog
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL
import rt.tt.temp.data.AppDatabase
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TimePicker
import androidx.compose.material3.rememberTimePickerState
import java.util.*
import java.util.Calendar
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import kotlinx.coroutines.launch
import java.io.File
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.ui.text.style.TextAlign
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material3.ListItem
import androidx.compose.material3.RadioButton
import androidx.compose.material.icons.outlined.Language
import androidx.compose.material.icons.outlined.Translate
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.foundation.clickable
import androidx.compose.material3.Divider
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Switch
import androidx.compose.material3.TextButton
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.MaterialTheme
import androidx.core.content.ContextCompat
import androidx.compose.foundation.layout.fillMaxWidth
import rt.tt.temp.utils.TranslationManager.DownloadState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers

// Format time for display (e.g., "10:00 AM")
private fun formatTimeForDisplay(hour: Int, minute: Int): String {
    val calendar = java.util.Calendar.getInstance().apply {
        set(java.util.Calendar.HOUR_OF_DAY, hour)
        set(java.util.Calendar.MINUTE, minute)
    }
    val timeFormat = SimpleDateFormat("h:mm a", Locale.getDefault())
    return timeFormat.format(calendar.time)
}

/**
 * Composable to display the download status of a language
 */
@Composable
private fun LanguageDownloadStatus(
    languageName: String,
    downloadState: DownloadState
) {
    Column {
        Text(
            text = languageName,
            style = MaterialTheme.typography.bodyMedium
        )
        Row(verticalAlignment = Alignment.CenterVertically) {
            when (downloadState) {
                DownloadState.DOWNLOADING -> {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "Downloading...",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
                DownloadState.DOWNLOADED -> {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "Downloaded",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
                DownloadState.ERROR -> {
                    Icon(
                        imageVector = Icons.Default.Error,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.error,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "Error",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
                else -> {
                    Icon(
                        imageVector = Icons.Default.Download,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "Not downloaded",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    themeViewModel: ThemeViewModel = viewModel(),
    translationViewModel: TranslationViewModel
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val prefs = remember { PreferenceManager.getDefaultSharedPreferences(context) }
    val scrollState = rememberScrollState()
    val notificationService = remember { NotificationService(context) }
    val isDarkTheme = themeViewModel.isDarkTheme.collectAsState()

    // Translation download states
    val englishDownloadState = translationViewModel.englishDownloadState.collectAsState()
    val banglaDownloadState = translationViewModel.banglaDownloadState.collectAsState()
    val arabicDownloadState = translationViewModel.arabicDownloadState.collectAsState()
    val libreTranslatorState = translationViewModel.libreTranslatorState.collectAsState()
    var isDownloading by remember { mutableStateOf(false) }
    val backupManager = remember { BackupManager(context) }
    var showBackupFrequencyDialog by remember { mutableStateOf(false) }
    var showRestoreDialog by remember { mutableStateOf(false) }
    var showDuplicateBackupDialog by remember { mutableStateOf(false) }
    var lastRestoredUri by remember { mutableStateOf<android.net.Uri?>(null) }
    var backupFrequency by remember {
        mutableStateOf(prefs.getInt("backup_frequency_hours", 48))
    }
    // Auto backup is enabled by default with 48-hour frequency
    var isAutoBackupEnabled by remember {
        mutableStateOf(prefs.getBoolean("auto_backup", true))
    }

    // Notification time settings
    var notificationHour by remember {
        mutableStateOf(prefs.getInt("notification_hour", 10)) // Default to 10 AM
    }
    var notificationMinute by remember {
        mutableStateOf(prefs.getInt("notification_minute", 0)) // Default to 00 minutes
    }
    var showTimePickerDialog by remember { mutableStateOf(false) }

    // Initialize auto backup if it's the first time
    LaunchedEffect(Unit) {
        // Check if this is the first run by looking for a specific flag
        val isFirstRun = prefs.getBoolean("first_run_backup_initialized", true)

        if (isFirstRun) {
            // Set default values for auto backup
            prefs.edit()
                .putBoolean("auto_backup", true)
                .putInt("backup_frequency_hours", 48)
                .putBoolean("first_run_backup_initialized", false)
                .apply()

            // Schedule the automatic backup
            backupManager.scheduleAutomaticBackup(48)

            // Update the state variables
            isAutoBackupEnabled = true
            backupFrequency = 48
        }
    }

    // Notification preferences
    var enableNotifications by remember {
        mutableStateOf(prefs.getBoolean("enable_notifications", true))
    }
    var notifyThreeMonths by remember {
        mutableStateOf(prefs.getBoolean("notify_three_months", true))
    }
    var notifyQuantityZero by remember {
        mutableStateOf(prefs.getBoolean("notify_quantity_zero", true))
    }
    var notifyLowStock by remember {
        mutableStateOf(prefs.getBoolean("notify_low_stock", true))
    }

    // Permission launcher for notifications
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission(),
        onResult = { isGranted ->
            enableNotifications = isGranted
            prefs.edit().putBoolean("enable_notifications", isGranted).apply()
            if (isGranted) {
                // Schedule notification worker when permissions are granted
                NotificationWorker.schedule(context)
                // Show demo notifications
                notificationService.showDemoNotifications()
            }
        }
    )

    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
            .padding(16.dp)
    ) {
        Spacer(modifier = Modifier.height(8.dp))

        // Appearance Section
        SettingsSection(
            title = "Appearance",
            icon = Icons.Outlined.Palette
        ) {
            SettingsCard {
                SettingsSwitch(
                    title = "Dark Mode",
                    subtitle = "Enable dark theme throughout the app",
                    icon = Icons.Outlined.DarkMode,
                    checked = isDarkTheme.value,
                    onCheckedChange = { isChecked ->
                        themeViewModel.setDarkTheme(isChecked)
                        prefs.edit().putBoolean("dark_mode", isChecked).apply()
                        (context as? Activity)?.recreate()
                    }
                )
            }
        }

        // Language Section
        SettingsSection(
            title = "Language",
            icon = Icons.Outlined.Language
        ) {
            SettingsCard {
                var showLanguageDialog by remember { mutableStateOf(false) }
                val currentLanguage = remember {
                    prefs.getString("app_language", Locale.getDefault().language) ?: "en"
                }

                SettingsClickable(
                    title = "App Language",
                    subtitle = when (currentLanguage) {
                        "en" -> "English"
                        "bn" -> "বাংলা"
                        else -> "English"
                    },
                    icon = Icons.Outlined.Translate,
                    onClick = { showLanguageDialog = true }
                )

                if (showLanguageDialog) {
                    AlertDialog(
                        onDismissRequest = { showLanguageDialog = false },
                        title = { Text("Select Language") },
                        text = {
                            Column(
                                modifier = Modifier
                                    .selectableGroup()
                                    .fillMaxWidth()
                            ) {
                                ListItem(
                                    headlineContent = { Text("English") },
                                    leadingContent = {
                                        RadioButton(
                                            selected = currentLanguage == "en",
                                            onClick = {
                                                prefs.edit().putString("app_language", "en").apply()
                                                showLanguageDialog = false
                                                (context as? Activity)?.recreate()
                                            }
                                        )
                                    }
                                )
                                ListItem(
                                    headlineContent = { Text("বাংলা") },
                                    leadingContent = {
                                        RadioButton(
                                            selected = currentLanguage == "bn",
                                            onClick = {
                                                prefs.edit().putString("app_language", "bn").apply()
                                                showLanguageDialog = false
                                                (context as? Activity)?.recreate()
                                            }
                                        )
                                    }
                                )
                            }
                        },
                        confirmButton = {
                            TextButton(onClick = { showLanguageDialog = false }) {
                                Text("Cancel")
                            }
                        }
                    )
                }

                Divider(modifier = Modifier.padding(vertical = 8.dp))

                // Translation Provider Section
                var showProviderDialog by remember { mutableStateOf(false) }
                val currentProvider by translationViewModel.translationProvider.collectAsState()

                SettingsClickable(
                    title = "Translation Provider",
                    subtitle = currentProvider.displayName,
                    icon = Icons.Outlined.Translate,
                    onClick = { showProviderDialog = true }
                )

                if (showProviderDialog) {
                    TranslationProviderDialog(
                        translationViewModel = translationViewModel,
                        onDismiss = { showProviderDialog = false }
                    )
                }

                Divider(modifier = Modifier.padding(vertical = 8.dp))

                // Test Translation Button
                SettingsClickable(
                    title = "Test Translation",
                    subtitle = "Verify that translation is working properly",
                    icon = Icons.Outlined.BugReport,
                    onClick = {
                        // Create a TranslationTester and run the test
                        val tester = TranslationTester(context)
                        tester.testLibreTranslator()
                    }
                )

                // Direct Translation Test
                SettingsClickable(
                    title = "Direct Translation Test",
                    subtitle = "Test translation without using the app's components",
                    icon = Icons.Outlined.Code,
                    onClick = {
                        // Run a direct translation test
                        CoroutineScope(Dispatchers.Main).launch {
                            Toast.makeText(context, "Testing direct translation...", Toast.LENGTH_SHORT).show()

                            try {
                                // Test Arabic to English
                                val arabicText = "مرحبا بالعالم"
                                val url = URL("https://libretranslate.com/translate")
                                val connection = url.openConnection() as HttpURLConnection
                                connection.requestMethod = "POST"
                                connection.setRequestProperty("Content-Type", "application/json")
                                connection.doOutput = true

                                val jsonRequest = JSONObject().apply {
                                    put("q", arabicText)
                                    put("source", "ar")
                                    put("target", "en")
                                    put("format", "text")
                                }

                                val outputStream = connection.outputStream
                                val writer = OutputStreamWriter(outputStream)
                                writer.write(jsonRequest.toString())
                                writer.flush()
                                writer.close()

                                val responseCode = connection.responseCode
                                if (responseCode == HttpURLConnection.HTTP_OK) {
                                    val inputStream = connection.inputStream
                                    val reader = BufferedReader(InputStreamReader(inputStream))
                                    val response = StringBuilder()
                                    var line: String?
                                    while (reader.readLine().also { line = it } != null) {
                                        response.append(line)
                                    }
                                    reader.close()

                                    val jsonResponse = JSONObject(response.toString())
                                    val translatedText = jsonResponse.optString("translatedText")

                                    Toast.makeText(
                                        context,
                                        "Direct translation: $arabicText -> $translatedText",
                                        Toast.LENGTH_LONG
                                    ).show()
                                } else {
                                    Toast.makeText(
                                        context,
                                        "Direct translation failed: $responseCode",
                                        Toast.LENGTH_LONG
                                    ).show()
                                }
                            } catch (e: Exception) {
                                Toast.makeText(
                                    context,
                                    "Direct translation error: ${e.message}",
                                    Toast.LENGTH_LONG
                                ).show()
                            }
                        }
                    }
                )

                Divider(modifier = Modifier.padding(vertical = 8.dp))

                // Language Download Section
                SettingsClickable(
                    title = "Download Translation Languages",
                    subtitle = "Download English and Bangla for offline translation",
                    icon = Icons.Outlined.Download,
                    onClick = {
                        if (!isDownloading) {
                            isDownloading = true
                            scope.launch {
                                try {
                                    val success = translationViewModel.downloadAllLanguages()
                                    if (success) {
                                        Toast.makeText(
                                            context,
                                            "Languages downloaded successfully",
                                            Toast.LENGTH_SHORT
                                        ).show()
                                    } else {
                                        Toast.makeText(
                                            context,
                                            "Failed to download languages",
                                            Toast.LENGTH_SHORT
                                        ).show()
                                    }
                                } catch (e: Exception) {
                                    Toast.makeText(
                                        context,
                                        "Error: ${e.message}",
                                        Toast.LENGTH_SHORT
                                    ).show()
                                } finally {
                                    isDownloading = false
                                }
                            }
                        }
                    }
                )

                // Download status indicators
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp)
                ) {
                    // Language download status row
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        // English status
                        LanguageDownloadStatus(
                            languageName = "English",
                            downloadState = englishDownloadState.value
                        )

                        // Bangla status
                        LanguageDownloadStatus(
                            languageName = "Bangla",
                            downloadState = banglaDownloadState.value
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    // Arabic and Libre Translator status
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        LanguageDownloadStatus(
                            languageName = "Arabic",
                            downloadState = arabicDownloadState.value
                        )

                        LanguageDownloadStatus(
                            languageName = "Libre Translator",
                            downloadState = libreTranslatorState.value
                        )
                    }
                }
            }
        }

        // Notification Settings Section
        SettingsSection(
            title = "Notifications",
            icon = Icons.Outlined.Notifications
        ) {
            SettingsCard {
                SettingsSwitch(
                    title = "Enable Notifications",
                    subtitle = "Receive alerts about expiring products and low stock",
                    icon = Icons.Outlined.NotificationsActive,
                    checked = prefs.getBoolean("notifications_enabled", true),
                    onCheckedChange = { isChecked ->
                        prefs.edit().putBoolean("notifications_enabled", isChecked).apply()
                        if (isChecked) {
                            // Re-schedule notifications if enabled
                            NotificationWorker.scheduleNotificationWorker(context, notificationHour, notificationMinute)
                        } else {
                            // Cancel notifications if disabled
                            NotificationWorker.cancelNotificationWorker(context)
                        }
                    }
                )

                AnimatedVisibility(
                    visible = prefs.getBoolean("notifications_enabled", true),
                    enter = expandVertically() + fadeIn(),
                    exit = shrinkVertically() + fadeOut()
                ) {
                    Column {
                        Divider()

                        // Notification Time Setting
                        SettingsClickable(
                            title = "Notification Time",
                            subtitle = formatTimeForDisplay(notificationHour, notificationMinute),
                            icon = Icons.Outlined.Schedule,
                            onClick = { showTimePickerDialog = true }
                        )
                    }
                }
            }
        }

        // Sound Settings Section
        SettingsSection(
            title = "Sound Feedback",
            icon = Icons.Outlined.VolumeUp
        ) {
            SettingsCard {
                SettingsSwitch(
                    title = "Sound Effects",
                    subtitle = "Play sound effects for actions",
                    icon = Icons.Outlined.MusicNote,
                    checked = prefs.getBoolean("sound_effects", true),
                    onCheckedChange = { isChecked ->
                        prefs.edit().putBoolean("sound_effects", isChecked).apply()
                    }
                )
            }
        }

        // Backup & Storage Section
        SettingsSection(
            title = "Backup & Storage",
            icon = Icons.Outlined.Backup
        ) {
            SettingsCard {
                Column {
                    SettingsSwitch(
                        title = "Auto Backup",
                        subtitle = "Automatically backup data periodically",
                        icon = Icons.Outlined.AutoMode,
                        checked = isAutoBackupEnabled,
                        onCheckedChange = { isChecked ->
                            isAutoBackupEnabled = isChecked  // Update the state
                            prefs.edit()
                                .putBoolean("auto_backup", isChecked)
                                .putInt("backup_frequency_hours", backupFrequency)
                                .apply()

                            if (isChecked) {
                                backupManager.scheduleAutomaticBackup(backupFrequency)
                            } else {
                                backupManager.cancelAutomaticBackup()
                            }
                        }
                    )

                    AnimatedVisibility(
                        visible = isAutoBackupEnabled,
                        enter = expandVertically() + fadeIn(),
                        exit = shrinkVertically() + fadeOut()
                    ) {
                        Divider(
                            modifier = Modifier.padding(vertical = 8.dp),
                            color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.5f)
                        )
                        SettingsClickable(
                            title = "Backup Frequency",
                            subtitle = "Every $backupFrequency hours",
                            icon = Icons.Outlined.Timer,
                            onClick = { showBackupFrequencyDialog = true }
                        )
                    }

                    Divider(
                        modifier = Modifier.padding(vertical = 8.dp),
                        color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.5f)
                    )

                    SettingsClickable(
                        title = "Create Backup Now",
                        subtitle = "Manually backup all data",
                        icon = Icons.Outlined.Save,
                        onClick = {
                            scope.launch {
                                try {
                                    val database = AppDatabase.getInstance(context)
                                    val backupFile = BackupUtils.createBackup(
                                        context = context,
                                        database = database,
                                        destinationFile = null  // Make sure createBackup accepts nullable parameter
                                    )
                                    if (backupFile != null) {
                                        // Show success message if needed
                                    }
                                } catch (e: Exception) {
                                    // Handle error if needed
                                }
                            }
                        }
                    )

                    Divider(
                        modifier = Modifier.padding(vertical = 8.dp),
                        color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.5f)
                    )

                    SettingsClickable(
                        title = "Restore Backup",
                        subtitle = "Restore data from a backup file",
                        icon = Icons.Outlined.Restore,
                        onClick = { showRestoreDialog = true }
                    )
                }
            }
        }

        // Enhanced Notification Settings Section
        SettingsSection(
            title = "Notifications",
            icon = Icons.Outlined.Notifications
        ) {
            SettingsCard {
                Column {
                    // Main notification toggle
                    SettingsSwitch(
                        title = "Enable Notifications",
                        subtitle = "Receive important product alerts",
                        icon = Icons.Outlined.NotificationsActive,
                        checked = enableNotifications,
                        onCheckedChange = { checked ->
                            enableNotifications = checked
                            prefs.edit().putBoolean("enable_notifications", checked).apply()
                            if (checked) {
                                NotificationWorker.schedule(context)
                            }
                        }
                    )

                    // Show additional notification options only if notifications are enabled
                    AnimatedVisibility(
                        visible = enableNotifications,
                        enter = expandVertically() + fadeIn(),
                        exit = shrinkVertically() + fadeOut()
                    ) {
                        Column {
                            Divider(
                                modifier = Modifier.padding(vertical = 8.dp),
                                color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.5f)
                            )

                            SettingsSwitch(
                                title = "Expiry Alerts",
                                subtitle = "Get notified when products are approaching expiry",
                                icon = Icons.Outlined.Timer,
                                checked = notifyThreeMonths,
                                onCheckedChange = { checked ->
                                    notifyThreeMonths = checked
                                    prefs.edit().putBoolean("notify_three_months", checked).apply()
                                    if (checked) {
                                        NotificationWorker.schedule(context)
                                    }
                                }
                            )

                            Divider(
                                modifier = Modifier.padding(vertical = 8.dp),
                                color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.5f)
                            )

                            SettingsSwitch(
                                title = "Low Stock Alerts",
                                subtitle = "Get notified when products are running low",
                                icon = Icons.Outlined.Inventory,
                                checked = notifyQuantityZero,
                                onCheckedChange = { checked ->
                                    notifyQuantityZero = checked
                                    prefs.edit().putBoolean("notify_quantity_zero", checked).apply()
                                    if (checked) {
                                        NotificationWorker.schedule(context)
                                    }
                                }
                            )

                            Divider(
                                modifier = Modifier.padding(vertical = 8.dp),
                                color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.5f)
                            )

                            SettingsSwitch(
                                title = "Low Stock Alerts",
                                subtitle = "Get notified when products fall below threshold",
                                icon = Icons.Outlined.Warning,
                                checked = notifyLowStock,
                                onCheckedChange = { checked ->
                                    notifyLowStock = checked
                                    prefs.edit().putBoolean("notify_low_stock", checked).apply()
                                    if (checked) {
                                        NotificationWorker.schedule(context)
                                    }
                                }
                            )

                            Divider(
                                modifier = Modifier.padding(vertical = 8.dp),
                                color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.5f)
                            )

                            // Demo notification button
                            FilledTonalButton(
                                onClick = {
                                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                                        when {
                                            ContextCompat.checkSelfPermission(
                                                context,
                                                Manifest.permission.POST_NOTIFICATIONS
                                            ) == PackageManager.PERMISSION_GRANTED -> {
                                                notificationService.showDemoNotifications()
                                            }
                                            else -> {
                                                permissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
                                            }
                                        }
                                    } else {
                                        notificationService.showDemoNotifications()
                                    }
                                },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = 16.dp, vertical = 8.dp),
                                enabled = enableNotifications
                            ) {
                                Icon(
                                    Icons.Outlined.Send,
                                    contentDescription = null,
                                    modifier = Modifier.padding(end = 8.dp)
                                )
                                Text("Send Test Notification")
                            }
                        }
                    }
                }
            }
        }

        // Advanced Settings Section
        SettingsSection(
            title = "Advanced",
            icon = Icons.Outlined.BuildCircle
        ) {
            SettingsCard {
                Column {
                    SettingsClickable(
                        title = "Clear Cache",
                        subtitle = "Free up space by clearing temporary files",
                        icon = Icons.Outlined.ClearAll,
                        onClick = { /* Implement cache clearing */ }
                    )

                    Divider(
                        modifier = Modifier.padding(vertical = 8.dp),
                        color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.5f)
                    )

                    SettingsClickable(
                        title = "Export Settings",
                        subtitle = "Save your settings configuration",
                        icon = Icons.Outlined.SaveAlt,
                        onClick = { /* Implement settings export */ }
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))
    }



    // Duplicate Backup Dialog
    if (showDuplicateBackupDialog) {
        AlertDialog(
            onDismissRequest = { showDuplicateBackupDialog = false },
            title = { Text("Duplicate Backup") },
            text = { Text("This backup has already been restored. Restoring it again may create duplicate data.") },
            confirmButton = {
                Button(
                    onClick = {
                        // Force restore by removing the backup ID from preferences
                        val backupPrefs = context.getSharedPreferences("backup_prefs", android.content.Context.MODE_PRIVATE)
                        val editor = backupPrefs.edit()
                        editor.remove("restored_backup_ids").apply()

                        // Close the dialog
                        showDuplicateBackupDialog = false

                        // Show a message
                        Toast.makeText(
                            context,
                            "Backup signature cleared. You can try restoring again.",
                            Toast.LENGTH_LONG
                        ).show()
                    }
                ) {
                    Text("Clear Backup History")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDuplicateBackupDialog = false }) {
                    Text("Cancel")
                }
            }
        )
    }

    // Time Picker Dialog
    if (showTimePickerDialog) {
        val timePickerState = rememberTimePickerState(
            initialHour = notificationHour,
            initialMinute = notificationMinute
        )

        AlertDialog(
            onDismissRequest = { showTimePickerDialog = false },
            title = { Text("Select Notification Time") },
            text = {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    TimePicker(state = timePickerState)
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        notificationHour = timePickerState.hour
                        notificationMinute = timePickerState.minute

                        // Save to preferences
                        prefs.edit()
                            .putInt("notification_hour", timePickerState.hour)
                            .putInt("notification_minute", timePickerState.minute)
                            .apply()

                        // Reschedule notifications with new time
                        if (prefs.getBoolean("notifications_enabled", true)) {
                            NotificationWorker.scheduleNotificationWorker(
                                context,
                                timePickerState.hour,
                                timePickerState.minute
                            )
                        }

                        showTimePickerDialog = false
                    }
                ) {
                    Text("OK")
                }
            },
            dismissButton = {
                TextButton(onClick = { showTimePickerDialog = false }) {
                    Text("Cancel")
                }
            }
        )
    }

    // Add Backup Frequency Dialog
    if (showBackupFrequencyDialog) {
        AlertDialog(
            onDismissRequest = { showBackupFrequencyDialog = false },
            title = { Text("Backup Frequency") },
            text = {
                Column {
                    listOf(6, 12, 24, 48, 72).forEach { hours ->
                        val isSelected = hours == backupFrequency
                        Button(
                            onClick = {
                                backupFrequency = hours
                                prefs.edit().putInt("backup_frequency_hours", hours).apply()
                                backupManager.scheduleAutomaticBackup(hours)
                                showBackupFrequencyDialog = false
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = if (isSelected) {
                                    MaterialTheme.colorScheme.primary
                                } else {
                                    MaterialTheme.colorScheme.surfaceVariant
                                },
                                contentColor = if (isSelected) {
                                    MaterialTheme.colorScheme.onPrimary
                                } else {
                                    MaterialTheme.colorScheme.onSurfaceVariant
                                }
                            )
                        ) {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text("Every $hours hours")
                                if (isSelected) {
                                    Icon(
                                        imageVector = Icons.Filled.Check,
                                        contentDescription = "Selected",
                                        modifier = Modifier.size(20.dp)
                                    )
                                }
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(onClick = { showBackupFrequencyDialog = false }) {
                    Text("Cancel")
                }
            }
        )
    }

    // Add Restore Dialog
    if (showRestoreDialog) {
        RestoreDialog(
            onDismiss = { showRestoreDialog = false },
            onRestore = { file ->
                scope.launch {
                    try {
                        val database = AppDatabase.getInstance(context)
                        val result = BackupUtils.restoreFromFile(context, database, file)
                        result.fold(
                            onSuccess = { restoreResult ->
                                if (restoreResult.isDuplicate) {
                                    // Show duplicate backup warning with a Toast
                                    Toast.makeText(
                                        context,
                                        "This backup has already been restored. Please use a different backup file.",
                                        Toast.LENGTH_LONG
                                    ).show()

                                    // Show a confirmation dialog in the UI thread
                                    showDuplicateBackupDialog = true
                                } else {
                                    // Normal success message
                                    val message = "Restore completed:\n" +
                                            "${restoreResult.newProductsCount} new products added\n" +
                                            "${restoreResult.mergedProductsCount} existing products merged\n" +
                                            "Total quantity added: ${restoreResult.totalQuantityAdded}"

                                    Toast.makeText(
                                        context,
                                        message,
                                        Toast.LENGTH_LONG
                                    ).show()
                                }
                            },
                            onFailure = { error ->
                                Toast.makeText(
                                    context,
                                    "Restore failed: ${error.message}",
                                    Toast.LENGTH_LONG
                                ).show()
                            }
                        )
                        showRestoreDialog = false
                    } catch (e: Exception) {
                        Log.e("SettingsScreen", "Restore failed", e)
                        Toast.makeText(
                            context,
                            "Restore failed: ${e.message}",
                            Toast.LENGTH_LONG
                        ).show()
                    }
                }
            }
        )
    }
}

@Composable
private fun SettingsCard(
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp
        )
    ) {
        content()
    }
}

@Composable
private fun SettingsSection(
    title: String,
    icon: ImageVector,
    content: @Composable () -> Unit
) {
    Column(
        modifier = Modifier.padding(vertical = 8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.width(16.dp))
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.primary
            )
        }
        content()
    }
}

@Composable
private fun SettingsSwitch(
    title: String,
    subtitle: String,
    icon: ImageVector,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            modifier = Modifier.weight(1f),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Column {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyLarge
                )
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange
        )
    }
}

@Composable
private fun SettingsClickable(
    title: String,
    subtitle: String,
    icon: ImageVector,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            modifier = Modifier.weight(1f),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Column {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyLarge
                )
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        Icon(
            imageVector = Icons.Outlined.ChevronRight,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
fun RestoreDialog(
    onDismiss: () -> Unit,
    onRestore: (File) -> Unit
) {
    val context = LocalContext.current
    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()  // Changed from OpenDocument
    ) { uri ->
        uri?.let { selectedUri ->
            // Convert URI to File
            val inputStream = context.contentResolver.openInputStream(selectedUri)
            val tempFile = File(context.cacheDir, "temp_backup.json")

            inputStream?.use { input ->
                tempFile.outputStream().use { output ->
                    input.copyTo(output)
                }
            }

            onRestore(tempFile)
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Restore Backup") },
        text = { Text("Select a backup file to restore") },
        confirmButton = {
            TextButton(
                onClick = {
                    launcher.launch("*/*")  // This will show all files, then we can filter in the UI
                }
            ) {
                Text("Choose File")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}
package rt.tt.temp.data

import androidx.room.*

@Dao
interface ProductCatalogDao {
    @Query("SELECT * FROM product_catalog WHERE barcode = :barcode")
    suspend fun getProductByBarcode(barcode: String): ProductCatalog?

    @Query("SELECT * FROM product_catalog WHERE barcode LIKE '%' || :lastSixDigits")
    suspend fun getProductBySixDigits(lastSixDigits: String): ProductCatalog?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProduct(product: ProductCatalog)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(products: List<ProductCatalog>)

    @Query("DELETE FROM product_catalog")
    suspend fun deleteAll()

    @Query("SELECT * FROM product_catalog")
    suspend fun getAllProducts(): List<ProductCatalog>

    @Query("SELECT COUNT(*) FROM product_catalog")
    suspend fun getProductCount(): Int
}

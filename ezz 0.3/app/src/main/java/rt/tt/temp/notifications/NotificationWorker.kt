package rt.tt.temp.notifications

import android.app.NotificationManager
import android.content.Context
import androidx.work.*
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.first
import rt.tt.temp.data.AppDatabase
import java.util.concurrent.TimeUnit
import java.util.Calendar

class NotificationWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {

    override suspend fun doWork(): Result = coroutineScope {
        val database = AppDatabase.getInstance(applicationContext)
        val notificationService = NotificationService(applicationContext)

        try {
            // Get current date for calculations
            val currentDate = System.currentTimeMillis()

            // Get all active products
            val allProducts = database.productDao().getAllActiveProducts(currentDate)
                .first() // Convert Flow to List

            // Filter products for expiry notifications
            val expiringProducts = allProducts.filter { product ->
                val daysUntilExpiry = ((product.expireDate - currentDate) / (1000 * 60 * 60 * 24)).toInt()
                !product.isExpired && daysUntilExpiry <= 90 // Show notifications for products expiring within 90 days
            }

            // Filter products for low stock notifications
            val lowStockProducts = allProducts.filter { product ->
                val stockPercentage = (product.currentQuantity.toFloat() / product.initialQuantity.toFloat()) * 100
                stockPercentage <= 25 // Show notifications for products with 25% or less stock
            }

            // Show notifications if there are products that need attention
            if (expiringProducts.isNotEmpty()) {
                notificationService.showExpiryNotifications(expiringProducts)
            }

            if (lowStockProducts.isNotEmpty()) {
                notificationService.showQuantityNotifications(lowStockProducts)
            }

            // If no products need attention, don't show any notifications
            if (expiringProducts.isEmpty() && lowStockProducts.isEmpty()) {
                // Cancel any existing notifications
                val notificationManager = applicationContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                notificationManager.cancelAll()
            }

            Result.success()
        } catch (e: Exception) {
            Result.failure()
        }
    }

    companion object {
        private const val NOTIFICATION_WORK_NAME = "product_notifications"

        /**
         * Schedule notifications at the default time (10:00 AM)
         */
        fun schedule(context: Context) {
            // Get saved notification time or use default (10:00 AM)
            val prefs = androidx.preference.PreferenceManager.getDefaultSharedPreferences(context)
            val hour = prefs.getInt("notification_hour", 10)
            val minute = prefs.getInt("notification_minute", 0)

            scheduleNotificationWorker(context, hour, minute)
        }

        /**
         * Schedule notifications at a specific time
         * @param hour Hour of day (0-23)
         * @param minute Minute (0-59)
         */
        fun scheduleNotificationWorker(context: Context, hour: Int, minute: Int) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                .build()

            // Calculate initial delay to the next occurrence of the specified time
            val calendar = Calendar.getInstance()
            val currentHour = calendar.get(Calendar.HOUR_OF_DAY)
            val currentMinute = calendar.get(Calendar.MINUTE)

            // Set target time
            calendar.set(Calendar.HOUR_OF_DAY, hour)
            calendar.set(Calendar.MINUTE, minute)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)

            // If the target time has already passed today, schedule for tomorrow
            if (currentHour > hour || (currentHour == hour && currentMinute >= minute)) {
                calendar.add(Calendar.DAY_OF_YEAR, 1)
            }

            // Calculate delay in milliseconds
            val now = System.currentTimeMillis()
            val delay = calendar.timeInMillis - now

            // Create work request with initial delay
            val dailyWorkRequest = PeriodicWorkRequestBuilder<NotificationWorker>(
                24, TimeUnit.HOURS
            )
            .setConstraints(constraints)
            .setInitialDelay(delay, TimeUnit.MILLISECONDS)
            .build()

            // Enqueue the work request
            WorkManager.getInstance(context)
                .enqueueUniquePeriodicWork(
                    NOTIFICATION_WORK_NAME,
                    ExistingPeriodicWorkPolicy.REPLACE,
                    dailyWorkRequest
                )
        }

        /**
         * Cancel scheduled notifications
         */
        fun cancelNotificationWorker(context: Context) {
            WorkManager.getInstance(context).cancelUniqueWork(NOTIFICATION_WORK_NAME)
        }
    }
}
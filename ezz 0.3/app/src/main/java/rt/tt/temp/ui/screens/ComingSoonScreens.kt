package rt.tt.temp.ui.screens

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier

@Composable
fun AnalyticsScreen() {
    ComingSoonScreen("Daily Sales Report")
}

@Composable
fun AttendanceScreen() {
    ComingSoonScreen("Attendance Sheet")
}

@Composable
fun IncentiveScreen() {
    ComingSoonScreen("Incentive Calculator")
}

@Composable
private fun ComingSoonScreen(feature: String) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Text("$feature - Coming Soon")
    }
}
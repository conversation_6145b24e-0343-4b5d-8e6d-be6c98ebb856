package rt.tt.temp.utils

import android.content.Context
import android.provider.Settings
import android.util.Log
import rt.tt.temp.data.AppDatabase
import rt.tt.temp.data.Product
import java.io.File
import java.io.FileWriter
import java.security.MessageDigest
import java.text.SimpleDateFormat
import java.util.*
import org.json.JSONObject
import org.json.JSONArray
import rt.tt.temp.utils.DirectoryUtils.BACKUP_FOLDER_NAME
import rt.tt.temp.utils.DirectoryUtils.getPublicAppDirectory
import android.os.Environment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

object BackupUtils {
    private const val TAG = "BackupUtils"

    private fun getPublicBackupDirectory(): File {
        val appFolder = getPublicAppDirectory()
        val backupDir = File(appFolder, BACKUP_FOLDER_NAME)

        try {
            if (!backupDir.exists()) {
                val success = backupDir.mkdirs()
                Log.d(TAG, "Creating backup folder: $success")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error creating backup directory", e)
        }

        return backupDir
    }

    suspend fun createBackup(
        context: Context,
        database: AppDatabase,
        destinationFile: File? = null
    ): File? {
        try {
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val backupDir = getPublicBackupDirectory()

            val backupFile = destinationFile ?: File(backupDir, "inventory_backup_$timestamp.json")

            // Get all products from database
            val products = database.productDao().getAllProducts()
            Log.d(TAG, "Creating backup with ${products.size} products")

            // Generate a unique backup ID (signature)
            val backupId = generateBackupId(context, products)

            // Create JSON object with backup metadata and products array
            val json = JSONObject()
            json.put("backup_date", System.currentTimeMillis())
            json.put("version", "1.0")
            json.put("backup_id", backupId)

            // Add products array
            val productsArray = JSONArray()
            products.forEach { product ->
                val productJson = JSONObject().apply {
                    put("id", product.id)
                    put("productName", product.productName)
                    put("branchName", product.branchName)
                    put("productType", product.productType)
                    put("barcode", product.barcode)
                    put("batch", product.batch)
                    put("initialQuantity", product.initialQuantity)
                    put("currentQuantity", product.currentQuantity)
                    put("mfgDate", product.mfgDate) // This is already a Long
                    put("expireDate", product.expireDate) // This is already a Long
                    put("createdAt", product.createdAt) // This is already a Long
                    put("isExpired", product.isExpired)
                }
                productsArray.put(productJson)
            }
            json.put("products", productsArray)

            // Write to file
            backupFile.parentFile?.mkdirs() // Ensure directory exists
            withContext(Dispatchers.IO) {
                FileWriter(backupFile).use { writer ->
                    writer.write(json.toString(2)) // Pretty print with 2-space indentation
                }
            }

            Log.d(TAG, "Backup created successfully at ${backupFile.absolutePath} with ID: $backupId")
            return backupFile
        } catch (e: Exception) {
            Log.e(TAG, "Error creating backup", e)
            return null
        }
    }

    fun getBackupFiles(context: Context): List<File> {
        val backupDir = getPublicBackupDirectory()
        return backupDir.listFiles()?.filter { it.name.endsWith(".json") }?.sortedByDescending { it.lastModified() }
            ?: emptyList()
    }

    suspend fun restoreFromFile(
        context: Context,
        database: AppDatabase,
        backupFile: File
    ): Result<RestoreResult> {
        return try {
            val jsonString = backupFile.readText()
            Log.d(TAG, "Reading backup file content: $jsonString")

            val json = JSONObject(jsonString)

            if (!json.has("products")) {
                Log.e(TAG, "Invalid backup format: 'products' key not found")
                return Result.failure(Exception("Invalid backup file format"))
            }

            // Check if this backup has a signature/ID
            val backupId = if (json.has("backup_id")) json.getString("backup_id") else null

            // Check if this backup has already been restored
            if (backupId != null && isBackupAlreadyRestored(context, backupId)) {
                Log.w(TAG, "This backup has already been restored (ID: $backupId)")
                return Result.success(
                    RestoreResult(
                        newProductsCount = 0,
                        mergedProductsCount = 0,
                        totalProductsProcessed = 0,
                        totalQuantityAdded = 0,
                        isDuplicate = true
                    )
                )
            }

            val productsArray = json.getJSONArray("products")
            Log.d(TAG, "Found ${productsArray.length()} products in backup")

            // Statistics for the restore operation
            var newProductsCount = 0
            var mergedProductsCount = 0
            var totalQuantityAdded = 0

            // Convert JSON to products list first
            val productsToRestore = mutableListOf<Product>()
            for (i in 0 until productsArray.length()) {
                val productJson = productsArray.getJSONObject(i)
                val product = Product(
                    id = 0, // Let Room assign new IDs
                    productName = productJson.getString("productName"),
                    productType = productJson.getString("productType"),
                    branchName = productJson.getString("branchName"),
                    barcode = productJson.getString("barcode"),
                    batch = productJson.getString("batch"),
                    initialQuantity = productJson.getInt("initialQuantity"),
                    currentQuantity = productJson.getInt("currentQuantity"),
                    mfgDate = productJson.getLong("mfgDate"), // Store as Long timestamp
                    expireDate = productJson.getLong("expireDate"), // Store as Long timestamp
                    isExpired = productJson.getBoolean("isExpired"),
                    createdAt = productJson.getLong("createdAt")
                )
                productsToRestore.add(product)
            }

            // Now perform the database operations with merging
            withContext(Dispatchers.IO) {
                // Get existing products from the database
                val existingProducts = database.productDao().getAllProductsSync()

                // Process each product from the backup
                for (newProduct in productsToRestore) {
                    // Check if a matching product exists
                    val matchingProduct = existingProducts.find { existing ->
                        // Match based on name, barcode, batch, and other identifying fields
                        existing.productName == newProduct.productName &&
                        existing.barcode == newProduct.barcode &&
                        existing.batch == newProduct.batch &&
                        existing.productType == newProduct.productType &&
                        existing.branchName == newProduct.branchName
                    }

                    if (matchingProduct != null) {
                        // Merge the products by adding quantities
                        val additionalQuantity = newProduct.currentQuantity
                        val updatedProduct = matchingProduct.copy(
                            initialQuantity = matchingProduct.initialQuantity + newProduct.initialQuantity,
                            currentQuantity = matchingProduct.currentQuantity + newProduct.currentQuantity
                        )

                        // Update the existing product
                        database.productDao().updateProduct(updatedProduct)

                        mergedProductsCount++
                        totalQuantityAdded += additionalQuantity
                        Log.d(TAG, "Merged product: ${newProduct.productName}, added quantity: $additionalQuantity")
                    } else {
                        // Insert as a new product
                        database.productDao().insertProduct(newProduct)
                        newProductsCount++
                        totalQuantityAdded += newProduct.currentQuantity
                        Log.d(TAG, "Added new product: ${newProduct.productName}")
                    }
                }
            }

            // Clean up temp file if it exists in cache
            if (backupFile.parentFile == context.cacheDir) {
                backupFile.delete()
            }

            // If we have a backup ID, store it to prevent duplicate restores
            if (backupId != null) {
                storeBackupId(context, backupId)
            }

            val result = RestoreResult(
                newProductsCount = newProductsCount,
                mergedProductsCount = mergedProductsCount,
                totalProductsProcessed = productsToRestore.size,
                totalQuantityAdded = totalQuantityAdded,
                isDuplicate = false
            )

            Log.d(TAG, "Restore completed: $result")
            Result.success(result)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to restore from backup", e)
            Result.failure(e)
        }
    }

    /**
     * Generate a unique backup ID based on the content and device information
     */
    private fun generateBackupId(context: Context, products: List<Product>): String {
        val contentSignature = products.joinToString("") {
            "${it.productName}:${it.barcode}:${it.batch}:${it.initialQuantity}:${it.currentQuantity}"
        }
        val deviceId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
        val timestamp = System.currentTimeMillis()

        val signatureData = "$contentSignature:$deviceId:$timestamp"
        return generateSHA256Hash(signatureData)
    }

    /**
     * Generate a SHA-256 hash of the input string
     */
    private fun generateSHA256Hash(input: String): String {
        val bytes = input.toByteArray()
        val md = MessageDigest.getInstance("SHA-256")
        val digest = md.digest(bytes)
        return digest.fold("", { str, it -> str + "%02x".format(it) })
    }

    /**
     * Store a backup ID in shared preferences
     */
    private fun storeBackupId(context: Context, backupId: String) {
        val prefs = context.getSharedPreferences("backup_prefs", Context.MODE_PRIVATE)
        val existingIds = prefs.getStringSet("restored_backup_ids", mutableSetOf<String>()) ?: mutableSetOf()

        // Store as a new set to ensure it's saved (SharedPreferences quirk)
        val updatedIds = mutableSetOf<String>().apply {
            addAll(existingIds)
            add(backupId)
        }

        prefs.edit().putStringSet("restored_backup_ids", updatedIds).apply()
        Log.d(TAG, "Stored backup ID: $backupId. Total IDs: ${updatedIds.size}")
    }

    /**
     * Check if a backup has already been restored
     */
    private fun isBackupAlreadyRestored(context: Context, backupId: String): Boolean {
        val prefs = context.getSharedPreferences("backup_prefs", Context.MODE_PRIVATE)
        val restoredIds = prefs.getStringSet("restored_backup_ids", mutableSetOf<String>()) ?: mutableSetOf()
        return restoredIds.contains(backupId)
    }

    /**
     * Data class to hold the results of a restore operation
     */
    data class RestoreResult(
        val newProductsCount: Int,
        val mergedProductsCount: Int,
        val totalProductsProcessed: Int,
        val totalQuantityAdded: Int,
        val isDuplicate: Boolean = false
    )

    private fun createJsonBackup(products: List<Product>): String {
        val json = JSONObject()
        json.put("backup_date", System.currentTimeMillis())
        json.put("version", "1.0")

        // Create JSONArray directly instead of using map
        val productsArray = JSONArray()
        products.forEach { product ->
            val productJson = JSONObject().apply {
                put("productName", product.productName)
                put("branchName", product.branchName)
                put("productType", product.productType)
                put("barcode", product.barcode)
                put("batch", product.batch)
                put("initialQuantity", product.initialQuantity)
                put("currentQuantity", product.currentQuantity)
                put("mfgDate", product.mfgDate)
                put("expireDate", product.expireDate)
                put("createdAt", product.createdAt)
                put("isExpired", product.isExpired)
            }
            productsArray.put(productJson)
        }

        json.put("products", productsArray)
        return json.toString(2) // Pretty print with indentation
    }

    fun getBackupDirectoryPath(): String {
        val downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
        return "${downloadsDir.absolutePath}/EXP TRACK/backup"
    }

    fun convertJsonToCSV(jsonFile: File): File {
        val jsonContent = jsonFile.readText()
        val json = JSONObject(jsonContent)
        val productsArray = json.getJSONArray("products")

        val csvFile = File(jsonFile.parentFile, "converted_inventory_${System.currentTimeMillis()}.csv")

        FileWriter(csvFile).use { writer ->
            // Write UTF-8 BOM for Excel compatibility
            writer.append('\ufeff')

            // Write table headers
            val headers = listOf(
                "Product Name",
                "Branch Name",
                "Product Type",
                "Barcode",
                "Batch",
                "Initial Quantity",
                "Current Quantity",
                "Manufacturing Date",
                "Expiry Date",
                "Created Date",
                "Is Expired"
            )
            writer.append(headers.joinToString(",") { escapeField(it) })
            writer.append("\n")

            // Convert each JSON product to CSV row
            for (i in 0 until productsArray.length()) {
                val product = productsArray.getJSONObject(i)

                // Convert timestamps to readable dates
                val mfgDate = Date(product.getLong("mfgDate"))
                val expireDate = Date(product.getLong("expireDate"))
                val createdDate = Date(product.getLong("createdAt"))

                val rowData = listOf(
                    product.getString("productName"),
                    product.getString("branchName"),
                    product.getString("productType"),
                    product.getString("barcode"),
                    product.getString("batch"),
                    product.getInt("initialQuantity").toString(),
                    product.getInt("currentQuantity").toString(),
                    SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(mfgDate),
                    SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(expireDate),
                    SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(createdDate),
                    product.getBoolean("isExpired").toString()
                ).map { escapeField(it) }

                writer.append(rowData.joinToString(","))
                writer.append("\n")
            }
        }

        return csvFile
    }

    private fun escapeField(field: String): String {
        return when {
            field.contains(",") || field.contains("\"") || field.contains("\n") || field.contains("\r") -> {
                "\"${field.replace("\"", "\"\"")}\""
            }
            else -> field
        }
    }
}

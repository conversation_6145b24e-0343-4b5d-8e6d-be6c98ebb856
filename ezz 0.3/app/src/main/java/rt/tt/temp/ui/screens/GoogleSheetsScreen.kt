package rt.tt.temp.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.border
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import rt.tt.temp.ui.theme.TempTheme
import rt.tt.temp.ui.viewmodels.GoogleSheetsViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GoogleSheetsScreen() {
    val context = LocalContext.current
    val viewModel: GoogleSheetsViewModel = remember { GoogleSheetsViewModel(context) }

    // Collect state from ViewModel
    val isConnected by viewModel.isConnected.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val lastSyncTime by viewModel.lastSyncTime.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    val operationStatus by viewModel.operationStatus.collectAsState()
    val productCount by viewModel.productCount.collectAsState()
    val availableBranches by viewModel.availableBranches.collectAsState()
    val selectedBranch by viewModel.selectedBranch.collectAsState()
    val branchProductCounts by viewModel.branchProductCounts.collectAsState()

    // Show error message if any
    errorMessage?.let { message ->
        LaunchedEffect(message) {
            // Error is displayed in the UI, auto-clear after 5 seconds
            kotlinx.coroutines.delay(5000)
            viewModel.clearError()
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // Main content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Header Section
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier
                        .padding(20.dp)
                        .fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Default.TableChart,
                        contentDescription = "Google Sheets",
                        modifier = Modifier.size(48.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    Text(
                        text = "Google Sheets Integration",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = "Export and sync your inventory data with Google Sheets",
                        style = MaterialTheme.typography.bodyMedium,
                        textAlign = TextAlign.Center,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
                    )
                }
            }

            // Connection Status Card
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 12.dp),
                colors = CardDefaults.cardColors(
                    containerColor = if (isConnected)
                        Color(0xFFE8F5E8)
                    else
                        MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = if (isConnected) Icons.Default.CheckCircle else Icons.Default.CloudOff,
                            contentDescription = null,
                            tint = if (isConnected) Color(0xFF4CAF50) else MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.size(24.dp)
                        )

                        Spacer(modifier = Modifier.width(12.dp))

                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = if (isConnected) "Connected to Google Sheets" else "Not Connected",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Medium,
                                color = if (isConnected) Color(0xFF2E7D32) else MaterialTheme.colorScheme.onSurfaceVariant
                            )

                            Text(
                                text = if (isConnected) {
                                    if (lastSyncTime != null) "Last sync: $lastSyncTime" else "Ready to sync"
                                } else {
                                    "Connect to start syncing with Google Sheets"
                                },
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                            )

                            // Show product count
                            if (productCount > 0) {
                                Text(
                                    text = "$productCount products in local database",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.primary,
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }
                    }

                    // Show operation status
                    operationStatus?.let { status ->
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = status,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Medium
                        )
                    }

                    // Show error message
                    errorMessage?.let { error ->
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = error,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.error,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }

            // Branch Selection Section
            if (availableBranches.isNotEmpty()) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 12.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Business,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "Branch Selection",
                                style = MaterialTheme.typography.titleSmall,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.onSecondaryContainer
                            )
                        }

                        Spacer(modifier = Modifier.height(12.dp))

                        // Branch selection dropdown
                        BranchSelectionDropdown(
                            branches = availableBranches,
                            selectedBranch = selectedBranch,
                            branchProductCounts = branchProductCounts,
                            onBranchSelected = { viewModel.selectBranch(it) }
                        )

                        // Selected branch info
                        selectedBranch?.let { branch ->
                            Spacer(modifier = Modifier.height(8.dp))
                            val productCount = branchProductCounts[branch] ?: 0
                            Text(
                                text = "Selected: $branch ($productCount products)",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.primary,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
            }

            // Quick Actions
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 12.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = {
                        if (isConnected) {
                            viewModel.disconnect()
                        } else {
                            viewModel.testConnection()
                        }
                    },
                    modifier = Modifier
                        .weight(1f)
                        .height(48.dp),
                    enabled = !isLoading,
                    colors = if (isConnected) {
                        ButtonDefaults.outlinedButtonColors()
                    } else {
                        ButtonDefaults.buttonColors()
                    }
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(18.dp),
                            strokeWidth = 2.dp,
                            color = MaterialTheme.colorScheme.onPrimary
                        )
                    } else {
                        Icon(
                            imageVector = if (isConnected) Icons.Default.CloudOff else Icons.Default.Link,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                    }
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(if (isConnected) "Disconnect" else "Connect")
                }

                OutlinedButton(
                    onClick = {
                        val branch = selectedBranch
                        if (branch != null) {
                            viewModel.exportBranchToSheets(branch)
                        } else {
                            viewModel.exportToSheets()
                        }
                    },
                    modifier = Modifier
                        .weight(1f)
                        .height(48.dp),
                    enabled = !isLoading && isConnected && productCount > 0
                ) {
                    Icon(
                        imageVector = Icons.Default.Upload,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(if (selectedBranch != null) "Export Branch" else "Export All")
                }

                OutlinedButton(
                    onClick = {
                        val branch = selectedBranch
                        if (branch != null) {
                            viewModel.importBranchFromSheets(branch)
                        } else {
                            viewModel.importFromSheets()
                        }
                    },
                    modifier = Modifier
                        .weight(1f)
                        .height(48.dp),
                    enabled = !isLoading && isConnected
                ) {
                    Icon(
                        imageVector = Icons.Default.Download,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(if (selectedBranch != null) "Import Branch" else "Import All")
                }
            }

            // Additional Sync Buttons
            if (isConnected) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 12.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = {
                            val branch = selectedBranch
                            if (branch != null) {
                                // Sync specific branch (export + import to ensure bidirectional sync)
                                viewModel.exportBranchToSheets(branch)
                            } else {
                                // Sync all branches
                                viewModel.syncAllBranchesToSheets()
                            }
                        },
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp),
                        enabled = !isLoading && availableBranches.isNotEmpty(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.secondary
                        )
                    ) {
                        Icon(
                            imageVector = if (selectedBranch != null) Icons.Default.Sync else Icons.Default.CloudSync,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            if (selectedBranch != null)
                                "Sync ${selectedBranch}"
                            else
                                "Sync All Branches"
                        )
                    }

                    OutlinedButton(
                        onClick = {
                            val branch = selectedBranch ?: "BAQQA"
                            viewModel.testBranchSync(branch)
                        },
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp),
                        enabled = !isLoading
                    ) {
                        Icon(
                            imageVector = Icons.Default.BugReport,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Debug Test")
                    }
                }
            }

            // Sheet Layout Preview Section
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Google Sheets Layout Preview",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )

                    Text(
                        text = "Your inventory data will be organized in the following structure:",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(bottom = 12.dp)
                    )

                    // Sheet Layout Table
                    SheetsLayoutTable()
                }
            }

            // Features Section
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Features",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )

                    FeatureItem(
                        icon = Icons.Default.Upload,
                        title = "Export to Sheets",
                        description = "Export your inventory data directly to Google Sheets"
                    )

                    FeatureItem(
                        icon = Icons.Default.Download,
                        title = "Import from Sheets",
                        description = "Import inventory data from existing Google Sheets"
                    )

                    FeatureItem(
                        icon = Icons.Default.Sync,
                        title = "Real-time Sync",
                        description = "Keep your data synchronized across all platforms"
                    )

                    FeatureItem(
                        icon = Icons.Default.Share,
                        title = "Easy Sharing",
                        description = "Share inventory reports with team members instantly"
                    )

                    FeatureItem(
                        icon = Icons.Default.Analytics,
                        title = "Advanced Analytics",
                        description = "Use Google Sheets' powerful analysis tools"
                    )

                    FeatureItem(
                        icon = Icons.Default.Schedule,
                        title = "Scheduled Exports",
                        description = "Automatically export data at regular intervals"
                    )
                }
            }
        }
    }
}

@Composable
private fun SheetsLayoutTable() {
    Column {
        // Sheet Tabs Preview
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            SheetTab("Products", isActive = true)
            SheetTab("Branches", isActive = false)
            SheetTab("Transactions", isActive = false)
            SheetTab("Reports", isActive = false)
        }

        // Main Products Sheet Layout
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
            )
        ) {
            Column(
                modifier = Modifier.padding(12.dp)
            ) {
                Text(
                    text = "Products Sheet Layout",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                // Scrollable table
                Row(
                    modifier = Modifier
                        .horizontalScroll(rememberScrollState())
                        .fillMaxWidth()
                ) {
                    // Table structure
                    Column {
                        // Header row
                        Row(
                            modifier = Modifier
                                .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.1f))
                                .padding(4.dp)
                        ) {
                            TableCell("ID", isHeader = true, width = 40.dp)
                            TableCell("Product Name", isHeader = true, width = 120.dp)
                            TableCell("Barcode", isHeader = true, width = 100.dp)
                            TableCell("Category", isHeader = true, width = 80.dp)
                            TableCell("Branch", isHeader = true, width = 80.dp)
                            TableCell("Quantity", isHeader = true, width = 70.dp)
                            TableCell("Mfg Date", isHeader = true, width = 80.dp)
                            TableCell("Exp Date", isHeader = true, width = 80.dp)
                            TableCell("Status", isHeader = true, width = 70.dp)
                        }

                        // Sample data rows
                        repeat(3) { index ->
                            Row(
                                modifier = Modifier
                                    .background(
                                        if (index % 2 == 0)
                                            MaterialTheme.colorScheme.surface
                                        else
                                            MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                                    )
                                    .padding(4.dp)
                            ) {
                                TableCell("${index + 1}", isHeader = false, width = 40.dp)
                                TableCell("Product ${index + 1}", isHeader = false, width = 120.dp)
                                TableCell("12345${index}", isHeader = false, width = 100.dp)
                                TableCell("Food", isHeader = false, width = 80.dp)
                                TableCell("Warehouse", isHeader = false, width = 80.dp)
                                TableCell("${100 - index * 10}", isHeader = false, width = 70.dp)
                                TableCell("01/01/24", isHeader = false, width = 80.dp)
                                TableCell("12/31/24", isHeader = false, width = 80.dp)
                                TableCell("Active", isHeader = false, width = 70.dp)
                            }
                        }

                        // More rows indicator
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(8.dp),
                            horizontalArrangement = Arrangement.Center
                        ) {
                            Text(
                                text = "... and more rows",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                                fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                            )
                        }
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(12.dp))

        // Additional Sheets Info
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            SheetInfo(
                title = "Branches",
                description = "Store branch information and locations",
                modifier = Modifier.weight(1f)
            )
            SheetInfo(
                title = "Transactions",
                description = "Track all inventory movements and changes",
                modifier = Modifier.weight(1f)
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            SheetInfo(
                title = "Reports",
                description = "Automated reports and analytics",
                modifier = Modifier.weight(1f)
            )
            SheetInfo(
                title = "Settings",
                description = "Configuration and sync preferences",
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
private fun SheetTab(name: String, isActive: Boolean) {
    Box(
        modifier = Modifier
            .background(
                if (isActive)
                    MaterialTheme.colorScheme.primary.copy(alpha = 0.2f)
                else
                    MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f),
                shape = androidx.compose.foundation.shape.RoundedCornerShape(
                    topStart = 4.dp,
                    topEnd = 4.dp
                )
            )
            .border(
                1.dp,
                if (isActive)
                    MaterialTheme.colorScheme.primary
                else
                    MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                shape = androidx.compose.foundation.shape.RoundedCornerShape(
                    topStart = 4.dp,
                    topEnd = 4.dp
                )
            )
            .padding(horizontal = 12.dp, vertical = 6.dp)
    ) {
        Text(
            text = name,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = if (isActive) FontWeight.Bold else FontWeight.Normal,
            color = if (isActive)
                MaterialTheme.colorScheme.primary
            else
                MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun TableCell(text: String, isHeader: Boolean, width: androidx.compose.ui.unit.Dp) {
    Box(
        modifier = Modifier
            .width(width)
            .border(
                0.5.dp,
                MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
            )
            .padding(4.dp),
        contentAlignment = Alignment.CenterStart
    ) {
        Text(
            text = text,
            style = if (isHeader)
                MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Bold)
            else
                MaterialTheme.typography.bodySmall,
            color = if (isHeader)
                MaterialTheme.colorScheme.primary
            else
                MaterialTheme.colorScheme.onSurface,
            maxLines = 1,
            overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis
        )
    }
}

@Composable
private fun SheetInfo(title: String, description: String, modifier: Modifier = Modifier) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
        )
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.8f),
                fontSize = 10.sp
            )
        }
    }
}

@Composable
private fun BranchSelectionDropdown(
    branches: List<String>,
    selectedBranch: String?,
    branchProductCounts: Map<String, Int>,
    onBranchSelected: (String?) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }

    Box {
        OutlinedButton(
            onClick = { expanded = true },
            modifier = Modifier.fillMaxWidth()
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = selectedBranch ?: "Select Branch (All)",
                    style = MaterialTheme.typography.bodyMedium
                )
                Icon(
                    imageVector = if (expanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                    contentDescription = null
                )
            }
        }

        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false },
            modifier = Modifier.fillMaxWidth()
        ) {
            // "All Branches" option
            DropdownMenuItem(
                text = {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text("All Branches")
                        Text(
                            text = "${branchProductCounts.values.sum()} total",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                },
                onClick = {
                    onBranchSelected(null)
                    expanded = false
                },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.SelectAll,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            )

            Divider()

            // Individual branches
            branches.forEach { branch ->
                DropdownMenuItem(
                    text = {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(branch)
                            Text(
                                text = "${branchProductCounts[branch] ?: 0} items",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    },
                    onClick = {
                        onBranchSelected(branch)
                        expanded = false
                    },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Business,
                            contentDescription = null,
                            tint = if (selectedBranch == branch)
                                MaterialTheme.colorScheme.primary
                            else
                                MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                )
            }
        }
    }
}

@Composable
private fun FeatureItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    description: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.Top
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(24.dp)
        )

        Spacer(modifier = Modifier.width(12.dp))

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun GoogleSheetsScreenPreview() {
    TempTheme {
        GoogleSheetsScreen()
    }
}

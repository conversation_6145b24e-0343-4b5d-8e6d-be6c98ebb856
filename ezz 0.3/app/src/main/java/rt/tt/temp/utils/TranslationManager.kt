package rt.tt.temp.utils

import android.content.Context
import android.util.Log
import com.google.mlkit.nl.translate.Translation
import com.google.mlkit.nl.translate.Translator
import com.google.mlkit.nl.translate.TranslatorOptions
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import rt.tt.temp.ui.viewmodels.Language
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import com.google.mlkit.nl.languageid.LanguageIdentification
import rt.tt.temp.utils.BanglaProductNames

/**
 * Manages translation functionality using ML Kit's on-device translation
 */
class TranslationManager(private val context: Context) {
    private val translators = mutableMapOf<Pair<String, String>, Translator>()
    private val TAG = "TranslationManager"

    // Language identification client
    private val languageIdentifier = LanguageIdentification.getClient()

    // Spell corrector for improving translation quality
    private val spellCorrector = SpellCorrector(context)

    // Libre Translator service
    private val libreTranslatorService = LibreTranslatorService()

    // Download states for each language
    private val _englishDownloadState = MutableStateFlow(DownloadState.NOT_DOWNLOADED)
    val englishDownloadState: StateFlow<DownloadState> = _englishDownloadState.asStateFlow()

    private val _banglaDownloadState = MutableStateFlow(DownloadState.NOT_DOWNLOADED)
    val banglaDownloadState: StateFlow<DownloadState> = _banglaDownloadState.asStateFlow()

    private val _arabicDownloadState = MutableStateFlow(DownloadState.NOT_DOWNLOADED)
    val arabicDownloadState: StateFlow<DownloadState> = _arabicDownloadState.asStateFlow()

    // Libre Translator state
    private val _libreTranslatorState = MutableStateFlow(DownloadState.NOT_DOWNLOADED)
    val libreTranslatorState: StateFlow<DownloadState> = _libreTranslatorState.asStateFlow()

    // Enum to represent download states
    enum class DownloadState {
        NOT_DOWNLOADED,
        DOWNLOADING,
        DOWNLOADED,
        ERROR
    }

    /**
     * Translates text from source language to target language
     * @param text The text to translate
     * @param sourceLanguage The source language code (e.g., "en", "ar", "bn")
     * @param targetLanguage The target language code
     * @return The translated text or the original text if translation fails
     */
    suspend fun translate(
        text: String,
        sourceLanguage: String,
        targetLanguage: String
    ): String = withContext(Dispatchers.IO) {
        try {
            // If source and target languages are the same, return the original text
            if (sourceLanguage == targetLanguage) {
                return@withContext text
            }

            // Check for English to Bangla translation using custom dictionary
            if (sourceLanguage == "en" && targetLanguage == "bn") {
                // First check for exact match
                if (BanglaProductNames.hasEnglishToBanglaTranslation(text)) {
                    val banglaText = BanglaProductNames.getBanglaName(text)
                    Log.d(TAG, "Found exact Bangla translation: '$text' -> '$banglaText'")
                    return@withContext banglaText ?: text
                }

                // Try fuzzy matching for product names
                val bestMatch = BanglaProductNames.findBestMatchForEnglish(text)
                if (bestMatch != null) {
                    val (matchedEnglish, banglaText) = bestMatch
                    Log.d(TAG, "Found fuzzy Bangla match: '$text' ~ '$matchedEnglish' -> '$banglaText'")
                    return@withContext banglaText
                }
            }

            // Check for Bangla to English translation using custom dictionary
            if (sourceLanguage == "bn" && targetLanguage == "en") {
                // First check for exact match
                if (BanglaProductNames.hasBanglaToEnglishTranslation(text)) {
                    val englishText = BanglaProductNames.getEnglishName(text)
                    Log.d(TAG, "Found exact English translation: '$text' -> '$englishText'")
                    return@withContext englishText ?: text
                }

                // Try fuzzy matching for product names
                val bestMatch = BanglaProductNames.findBestMatchForBangla(text)
                if (bestMatch != null) {
                    val (matchedBangla, englishText) = bestMatch
                    Log.d(TAG, "Found fuzzy English match: '$text' ~ '$matchedBangla' -> '$englishText'")
                    return@withContext englishText
                }
            }

            // Apply spell correction if source language is English
            val correctedText = if (sourceLanguage == "en") {
                val corrected = spellCorrector.correctText(text)
                if (corrected != text) {
                    Log.d(TAG, "Spell corrected: '$text' -> '$corrected'")
                }
                corrected
            } else {
                text
            }

            // Get translator for the language pair
            val translator = getTranslator(sourceLanguage, targetLanguage)

            // Download model if needed and translate
            downloadModelIfNeeded(translator)
            val translatedText = translateText(translator, correctedText)

            // Log the translation for debugging
            Log.d(TAG, "Translated (with spell correction): '$text' -> '$translatedText'")

            translatedText
        } catch (e: Exception) {
            Log.e(TAG, "Translation failed: ${e.message}", e)
            text // Return original text on failure
        }
    }

    private suspend fun downloadModelIfNeeded(translator: Translator) = suspendCancellableCoroutine { continuation ->
        translator.downloadModelIfNeeded()
            .addOnSuccessListener {
                if (continuation.isActive) continuation.resume(Unit)
            }
            .addOnFailureListener { exception ->
                if (continuation.isActive) continuation.resumeWithException(exception)
            }
    }

    private suspend fun translateText(translator: Translator, text: String) = suspendCancellableCoroutine { continuation ->
        translator.translate(text)
            .addOnSuccessListener { translatedText ->
                if (continuation.isActive) continuation.resume(translatedText)
            }
            .addOnFailureListener { exception ->
                if (continuation.isActive) continuation.resumeWithException(exception)
            }
    }

    /**
     * Downloads language models for English, Bangla, and Arabic
     * @return True if download was successful, false otherwise
     */
    suspend fun downloadAllLanguages(): Boolean = withContext(Dispatchers.IO) {
        try {
            // Download English models
            _englishDownloadState.value = DownloadState.DOWNLOADING
            val englishToBangla = getTranslator("en", "bn")
            val englishToArabic = getTranslator("en", "ar")
            downloadModelIfNeeded(englishToBangla)
            downloadModelIfNeeded(englishToArabic)
            _englishDownloadState.value = DownloadState.DOWNLOADED

            // Download Bangla models
            _banglaDownloadState.value = DownloadState.DOWNLOADING
            val banglaToEnglish = getTranslator("bn", "en")
            val banglaToArabic = getTranslator("bn", "ar")
            downloadModelIfNeeded(banglaToEnglish)
            downloadModelIfNeeded(banglaToArabic)
            _banglaDownloadState.value = DownloadState.DOWNLOADED

            // Download Arabic models
            _arabicDownloadState.value = DownloadState.DOWNLOADING
            val arabicToEnglish = getTranslator("ar", "en")
            val arabicToBangla = getTranslator("ar", "bn")
            downloadModelIfNeeded(arabicToEnglish)
            downloadModelIfNeeded(arabicToBangla)
            _arabicDownloadState.value = DownloadState.DOWNLOADED

            true
        } catch (e: Exception) {
            Log.e(TAG, "Error downloading language models: ${e.message}", e)
            _englishDownloadState.value = DownloadState.ERROR
            _banglaDownloadState.value = DownloadState.ERROR
            _arabicDownloadState.value = DownloadState.ERROR
            false
        }
    }

    /**
     * Check if a specific language model is downloaded
     * @param language The language to check
     * @return True if the model is downloaded, false otherwise
     */
    suspend fun isLanguageDownloaded(language: Language): Boolean = withContext(Dispatchers.IO) {
        try {
            val translator = when (language) {
                Language.ENGLISH -> getTranslator("en", "bn")
                Language.BANGLA -> getTranslator("bn", "en")
                Language.ARABIC -> getTranslator("ar", "en")
            }

            // Check if model is downloaded by attempting to translate a test string
            try {
                // If model is not downloaded, this will trigger a download
                downloadModelIfNeeded(translator)
                true
            } catch (e: Exception) {
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking if language is downloaded: ${e.message}", e)
            false
        }
    }

    /**
     * Get a translator for the given language pair
     */
    private fun getTranslator(sourceLanguage: String, targetLanguage: String): Translator {
        val translatorKey = Pair(sourceLanguage, targetLanguage)
        return translators.getOrPut(translatorKey) {
            val options = TranslatorOptions.Builder()
                .setSourceLanguage(sourceLanguage)
                .setTargetLanguage(targetLanguage)
                .build()
            Translation.getClient(options)
        }
    }

    /**
     * Identify the language of a text
     * @param text The text to identify
     * @return The language code (e.g., "en", "ar", "bn") or "en" as fallback
     */
    suspend fun identifyLanguage(text: String): String = suspendCancellableCoroutine { continuation ->
        // Simple check for Arabic script (more reliable than ML Kit for short texts)
        if (containsArabicScript(text)) {
            Log.d(TAG, "Text contains Arabic script, identifying as Arabic")
            if (continuation.isActive) continuation.resume("ar")
            return@suspendCancellableCoroutine
        }

        // Simple check for Bengali script
        if (containsBengaliScript(text)) {
            Log.d(TAG, "Text contains Bengali script, identifying as Bengali")
            if (continuation.isActive) continuation.resume("bn")
            return@suspendCancellableCoroutine
        }

        // For other cases, use ML Kit
        languageIdentifier.identifyLanguage(text)
            .addOnSuccessListener { languageCode ->
                if (continuation.isActive) {
                    if (languageCode == "und" || languageCode.isEmpty()) {
                        // Language could not be identified, default to English
                        Log.d(TAG, "Language could not be identified, defaulting to English")
                        continuation.resume("en")
                    } else {
                        Log.d(TAG, "Language identified as: $languageCode")
                        continuation.resume(languageCode)
                    }
                }
            }
            .addOnFailureListener { exception ->
                if (continuation.isActive) {
                    Log.e(TAG, "Language identification failed: ${exception.message}", exception)
                    continuation.resume("en") // Default to English on failure
                }
            }
    }

    /**
     * Check if text contains Arabic script
     */
    private fun containsArabicScript(text: String): Boolean {
        // Arabic Unicode block ranges
        val arabicRanges = listOf(
            '\u0600'..'\u06FF', // Arabic
            '\u0750'..'\u077F', // Arabic Supplement
            '\u08A0'..'\u08FF', // Arabic Extended-A
            '\uFB50'..'\uFDFF', // Arabic Presentation Forms-A
            '\uFE70'..'\uFEFF'  // Arabic Presentation Forms-B
        )

        return text.any { char ->
            arabicRanges.any { range -> char in range }
        }
    }

    /**
     * Check if text contains Bengali script
     */
    private fun containsBengaliScript(text: String): Boolean {
        // Bengali Unicode block range
        val bengaliRange = '\u0980'..'\u09FF'

        return text.any { char -> char in bengaliRange }
    }

    /**
     * Translate text using Libre Translator
     */
    suspend fun translateWithLibre(
        text: String,
        sourceLanguage: String,
        targetLanguage: String
    ): String {
        // Update state to show we're using the service
        _libreTranslatorState.value = DownloadState.DOWNLOADING

        try {
            // Log the request
            Log.d(TAG, "LibreTranslator: Translating '$text' from $sourceLanguage to $targetLanguage")

            // Check if the text is empty or just whitespace
            if (text.isBlank()) {
                Log.d(TAG, "LibreTranslator: Text is blank, skipping translation")
                _libreTranslatorState.value = DownloadState.DOWNLOADED
                return text
            }

            // Check if source and target languages are the same
            if (sourceLanguage == targetLanguage) {
                Log.d(TAG, "LibreTranslator: Source and target languages are the same, skipping translation")
                _libreTranslatorState.value = DownloadState.DOWNLOADED
                return text
            }

            // Force specific language mappings for better compatibility
            val mappedSourceLang = when (sourceLanguage) {
                "ar" -> "ar"
                "bn" -> "bn"
                else -> "en"
            }

            val mappedTargetLang = when (targetLanguage) {
                "ar" -> "ar"
                "bn" -> "bn"
                else -> "en"
            }

            Log.d(TAG, "LibreTranslator: Mapped languages - source: $mappedSourceLang, target: $mappedTargetLang")

            // Perform the translation directly
            val result = libreTranslatorService.translate(text, mappedSourceLang, mappedTargetLang)

            // Check if translation was successful
            if (result != text && result.isNotBlank()) {
                Log.d(TAG, "LibreTranslator: Translation successful - '$result'")
                _libreTranslatorState.value = DownloadState.DOWNLOADED
                return result
            } else {
                Log.w(TAG, "LibreTranslator: Translation returned same text or empty result")
                _libreTranslatorState.value = DownloadState.ERROR
                return text
            }
        } catch (e: Exception) {
            Log.e(TAG, "LibreTranslator: Translation failed - ${e.message}", e)
            _libreTranslatorState.value = DownloadState.ERROR
            return text
        }
    }

    /**
     * Check if Libre Translator supports a language
     */
    fun isLibreLanguageSupported(languageCode: String): Boolean {
        return libreTranslatorService.isLanguageSupported(languageCode)
    }

    /**
     * Closes all translators and resources
     */
    fun shutdown() {
        translators.values.forEach { it.close() }
        translators.clear()
        languageIdentifier.close()
    }
}

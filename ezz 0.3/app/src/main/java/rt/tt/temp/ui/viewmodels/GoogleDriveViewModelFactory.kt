package rt.tt.temp.ui.viewmodels

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider

class GoogleDriveViewModelFactory(private val context: Context) : ViewModelProvider.Factory {
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(GoogleDriveViewModel::class.java)) {
            return GoogleDriveViewModel(context) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}

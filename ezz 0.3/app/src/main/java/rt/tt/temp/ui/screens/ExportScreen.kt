package rt.tt.temp.ui.screens

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.filled.OpenInNew
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import rt.tt.temp.R
import rt.tt.temp.data.Product
import rt.tt.temp.data.AppDatabase
import rt.tt.temp.ui.viewmodels.ExportViewModel
import rt.tt.temp.ui.viewmodels.ExportState
import rt.tt.temp.utils.ExportType
import rt.tt.temp.utils.exportFile
import rt.tt.temp.utils.openFile
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ExportScreen(
    products: List<Product>,
    database: AppDatabase,
    onBackupComplete: (destinationFile: File) -> Unit
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val exportViewModel = remember { ExportViewModel() }
    val exportState by exportViewModel.exportState.collectAsState()

    var selectedExportType by remember { mutableStateOf(ExportType.EXCEL) }
    var showExportConfirmation by remember { mutableStateOf(false) }

    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Header
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 24.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.7f)
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Filled.CloudUpload,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(48.dp)
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = stringResource(R.string.export_data),
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "${products.size} ${stringResource(R.string.items_available_for_export)}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                    )
                }
            }

            // Export Format Options
            Text(
                text = stringResource(R.string.select_export_format),
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 8.dp)
            )

            // Export Format Cards
            ExportFormatCard(
                type = ExportType.EXCEL,
                icon = Icons.Outlined.TableChart,
                title = "Excel (.csv)",
                description = "Spreadsheet format compatible with Microsoft Excel and other spreadsheet applications",
                isSelected = selectedExportType == ExportType.EXCEL,
                onClick = { selectedExportType = ExportType.EXCEL }
            )

            ExportFormatCard(
                type = ExportType.PDF,
                icon = Icons.Outlined.PictureAsPdf,
                title = "PDF (.pdf)",
                description = "Portable Document Format for viewing on any device",
                isSelected = selectedExportType == ExportType.PDF,
                onClick = { selectedExportType = ExportType.PDF }
            )

            ExportFormatCard(
                type = ExportType.JSON,
                icon = Icons.Outlined.Code,
                title = "JSON (.json)",
                description = "JavaScript Object Notation format for data interchange",
                isSelected = selectedExportType == ExportType.JSON,
                onClick = { selectedExportType = ExportType.JSON }
            )

            Spacer(modifier = Modifier.height(24.dp))

            // Export Button
            Button(
                onClick = { showExportConfirmation = true },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Icon(
                    imageVector = Icons.Filled.FileDownload,
                    contentDescription = null,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = stringResource(R.string.start_export),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Export Status
            when(val state = exportState) {
                is ExportState.Loading -> {
                    StatusCard(
                        icon = Icons.Filled.Sync,
                        iconTint = MaterialTheme.colorScheme.primary,
                        title = stringResource(R.string.exporting_please_wait),
                        showProgress = true
                    )
                }

                is ExportState.Success -> {
                    StatusCard(
                        icon = Icons.Filled.CheckCircle,
                        iconTint = Color(0xFF4CAF50),
                        title = stringResource(R.string.export_successful),
                        message = "${state.file.name}",
                        actionText = "Open File",
                        onAction = {
                            // Open the exported file
                            context.openFile(state.file)
                        }
                    )
                }

                is ExportState.Error -> {
                    StatusCard(
                        icon = Icons.Filled.Error,
                        iconTint = MaterialTheme.colorScheme.error,
                        title = stringResource(R.string.export_failed),
                        message = state.message
                    )
                }

                ExportState.Idle -> {
                    // No status to show when idle
                }
            }
        }

        // Export Confirmation Dialog
        if (showExportConfirmation) {
            AlertDialog(
                onDismissRequest = { showExportConfirmation = false },
                icon = {
                    Icon(
                        imageVector = Icons.Filled.FileDownload,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary
                    )
                },
                title = {
                    Text(
                        text = "Confirm Export",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                },
                text = {
                    Column {
                        Text("You are about to export ${products.size} items as ${selectedExportType.name.lowercase()}")
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Format: ${selectedExportType.name}",
                            fontWeight = FontWeight.Medium
                        )
                    }
                },
                confirmButton = {
                    Button(
                        onClick = {
                            showExportConfirmation = false
                            when(selectedExportType) {
                                ExportType.EXCEL -> {
                                    // Use CSV export functionality for Excel
                                    scope.launch {
                                        try {
                                            // Use the CSV export function but with EXCEL type
                                            val file = context.exportFile(products, ExportType.EXCEL)
                                            exportViewModel.updateExportState(ExportState.Success(file))
                                        } catch (e: Exception) {
                                            exportViewModel.updateExportState(ExportState.Error(e.message ?: "Export failed"))
                                        }
                                    }
                                }
                                ExportType.JSON -> {
                                    scope.launch {
                                        try {
                                            val file = context.exportFile(products, ExportType.JSON)
                                            exportViewModel.updateExportState(ExportState.Success(file))
                                        } catch (e: Exception) {
                                            exportViewModel.updateExportState(ExportState.Error(e.message ?: "Export failed"))
                                        }
                                    }
                                }
                                ExportType.PDF -> {
                                    scope.launch {
                                        try {
                                            val file = context.exportFile(products, ExportType.PDF)
                                            exportViewModel.updateExportState(ExportState.Success(file))
                                        } catch (e: Exception) {
                                            exportViewModel.updateExportState(ExportState.Error(e.message ?: "Export failed"))
                                        }
                                    }
                                }
                            }
                        }
                    ) {
                        Text("Export")
                    }
                },
                dismissButton = {
                    OutlinedButton(
                        onClick = { showExportConfirmation = false }
                    ) {
                        Text("Cancel")
                    }
                }
            )
        }
    }

}

@Composable
private fun ExportFormatCard(
    type: ExportType,
    icon: ImageVector,
    title: String,
    description: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 6.dp)
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.7f)
            } else {
                MaterialTheme.colorScheme.surface
            }
        ),
        border = if (isSelected) {
            BorderStroke(2.dp, MaterialTheme.colorScheme.primary)
        } else {
            null
        },
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isSelected) 4.dp else 1.dp
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Selection indicator
            RadioButton(
                selected = isSelected,
                onClick = onClick,
                colors = RadioButtonDefaults.colors(
                    selectedColor = MaterialTheme.colorScheme.primary
                )
            )

            Spacer(modifier = Modifier.width(8.dp))

            // Icon
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = if (isSelected) {
                    MaterialTheme.colorScheme.primary
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant
                },
                modifier = Modifier.size(32.dp)
            )

            Spacer(modifier = Modifier.width(16.dp))

            // Text content
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Medium,
                    color = if (isSelected) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.onSurface
                    }
                )

                Spacer(modifier = Modifier.height(2.dp))

                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun StatusCard(
    icon: ImageVector,
    iconTint: Color,
    title: String,
    message: String? = null,
    showProgress: Boolean = false,
    actionText: String? = null,
    onAction: (() -> Unit)? = null
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (showProgress) {
                CircularProgressIndicator(
                    modifier = Modifier.size(48.dp),
                    color = iconTint
                )
            } else {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = iconTint,
                    modifier = Modifier.size(48.dp)
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )

            if (message != null) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = message,
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            if (actionText != null && onAction != null) {
                Spacer(modifier = Modifier.height(16.dp))
                Button(
                    onClick = onAction,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    ),
                    modifier = Modifier.fillMaxWidth(0.7f)
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Icon(
                            imageVector = Icons.Filled.OpenInNew,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = actionText,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }
    }
}
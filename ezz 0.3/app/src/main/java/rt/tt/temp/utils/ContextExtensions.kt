package rt.tt.temp.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log
import androidx.core.content.FileProvider
import rt.tt.temp.data.Product
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

fun Context.shareFile(file: File, mimeType: String) {
    try {
        val uri = FileProvider.getUriForFile(
            this,
            "${packageName}.provider",
            file
        )

        val intent = Intent(Intent.ACTION_SEND).apply {
            type = mimeType
            putExtra(Intent.EXTRA_STREAM, uri)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            putExtra(Intent.EXTRA_SUBJECT, "Inventory Export - ${SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())}")
        }

        startActivity(Intent.createChooser(intent, "Share Export"))
    } catch (e: Exception) {
        Log.e("ContextExtensions", "Error sharing file", e)
        throw Exception("Failed to share file: ${e.message}", e)
    }
}

suspend fun Context.exportFile(products: List<Product>, exportType: ExportType): File {
    try {
        return when (exportType) {
            ExportType.EXCEL -> {
                // Use CSV export for Excel
                val directory = DirectoryUtils.getCsvExportDirectory()
                val file = ExportUtils.exportProductsToCSV(products, directory)
                shareFile(file, "text/csv")
                file
            }
            ExportType.PDF -> {
                val directory = DirectoryUtils.getPdfExportDirectory()
                val file = PdfUtils.exportProductsToPDF(products, directory)
                shareFile(file, "application/pdf")
                file
            }
            ExportType.JSON -> {
                val directory = DirectoryUtils.getJsonExportDirectory()
                val file = ExportUtils.exportProductsToJSON(products, directory)
                shareFile(file, "application/json")
                file
            }
        }
    } catch (e: Exception) {
        Log.e("ContextExtensions", "Error during file export", e)
        throw e
    }
}

fun Context.openExportDirectoryDirect() {
    val exportDir = DirectoryUtils.getPublicExportDirectory()
    val intent = Intent(Intent.ACTION_VIEW).apply {
        setDataAndType(Uri.fromFile(exportDir), "resource/folder")
    }
    startActivity(intent)
}

fun Context.openFile(file: File) {
    try {
        val uri = FileProvider.getUriForFile(
            this,
            "${packageName}.provider",
            file
        )

        val intent = Intent(Intent.ACTION_VIEW).apply {
            setDataAndType(uri, getMimeType(file))
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }

        startActivity(Intent.createChooser(intent, "Open File"))
    } catch (e: Exception) {
        Log.e("ContextExtensions", "Error opening file", e)
        // Show a toast or other notification to the user
    }
}

private fun Context.getMimeType(file: File): String {
    return when {
        file.name.endsWith(".pdf", ignoreCase = true) -> "application/pdf"
        file.name.endsWith(".csv", ignoreCase = true) -> "text/csv"
        file.name.endsWith(".json", ignoreCase = true) -> "application/json"
        file.name.endsWith(".xlsx", ignoreCase = true) -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        file.name.endsWith(".txt", ignoreCase = true) -> "text/plain"
        else -> "*/*"
    }
}
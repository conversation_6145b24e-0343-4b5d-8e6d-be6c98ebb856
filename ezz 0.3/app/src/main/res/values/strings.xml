<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Add these missing strings -->
    <string name="export_data">Export Data</string>
    <string name="select_export_format">Select Export Format</string>
    <string name="export_format">Export Format</string>
    <string name="start_export">Start Export</string>
    <string name="exporting_please_wait">Exporting, please wait...</string>
    <string name="export_successful">Export Successful</string>
    <string name="export_failed">Export Failed</string>
    <string name="select_export_format_to_begin">Please select an export format to begin</string>
    <string name="items_available_for_export">items available for export</string>

    <string name="expiry_overview">Expiry Overview</string>
    <string name="language">Language</string>
    <string name="app_language">App Language</string>
    <string name="select_language">Select Language</string>

    <!-- Dashboard Summary Cards -->

    <string name="app_name">Inventory Expire Track</string>
    <!-- Main Menu Items -->
    <string name="dashboard">Dashboard</string>
    <string name="all_items">All Items</string>
    <string name="new_entry">New Entry</string>
    <string name="settings">Settings</string>
    <string name="about">About</string>
    <string name="multi_user">Multi User</string>
    <string name="search">Search</string>
    <string name="search_products">Search products…</string>

    <!-- Sort Options -->
    <string name="sort">Sort</string>
    <string name="name_asc">Name (A to Z)</string>
    <string name="name_desc">Name (Z to A)</string>
    <string name="expiry_nearest">Expiry Date (Nearest)</string>
    <string name="expiry_farthest">Expiry Date (Farthest)</string>
    <string name="quantity_high">Quantity (High to Low)</string>
    <string name="quantity_low">Quantity (Low to High)</string>
    <string name="branch_asc">Branch (A to Z)</string>
    <string name="branch_desc">Branch (Z to A)</string>

    <!-- Dashboard Section Titles -->
    <string name="upcoming_expiries">Upcoming Expiries</string>
    <string name="no_upcoming_expiries">No upcoming expiries</string>
    <string name="units">units</string>
    <string name="expired">EXPIRED</string>
    <string name="days_left">%d days left</string>
    <string name="expiring_products">Expiring Soon</string>
    <string name="near_expiry">Near Expiry</string>
    <string name="in_stock">Sufficient Date</string>
    <string name="total_items">Total Items</string>

    <!-- Branch Names -->
    <string name="branch_warehouse">WAREHOUSE</string>
    <string name="branch_jumlah">JUMLAH</string>
    <string name="branch_bairut">BAIRUT</string>
    <string name="branch_shifa">SHIFA</string>
    <string name="branch_qafer">QAFER</string>
    <string name="branch_madain">MADAIN</string>
    <string name="branch_aja">AJA</string>
    <string name="branch_baqqa">BAQAA</string>

    <!-- New Entry Form Labels -->
    <string name="branch_information">Branch Information</string>
    <string name="branch_name">Branch Name</string>
    <string name="product_information">Product Information</string>
    <string name="product_name">Product Name</string>
    <string name="product_type">Product Type</string>
    <string name="barcode">Barcode</string>
    <string name="batch">Batch (HH:MM)</string>
    <string name="quantity">Quantity(PRESENT)</string>
    <string name="manufacturing_date">Manufacturing Date</string>
    <string name="expiry_date">Expiry Date</string>
    <string name="include_day">Include Day</string>
    <string name="save">Save</string>
    <string name="cancel">Cancel</string>
    <string name="ok">OK</string>

    <!-- Dialog Titles -->
    <string name="add_new_product">Add New Product</string>
    <string name="error">Error</string>

    <!-- Error Messages -->
    <string name="error_branch_required">Branch name is required</string>
    <string name="error_product_required">Product name is required</string>
    <string name="error_barcode_required">Barcode is required</string>
    <string name="error_batch_format">Invalid batch format. Use HH:MM</string>
    <string name="error_invalid_date">Invalid date format</string>
    <string name="error_expiry_before_mfg">Expiry date must be after manufacturing date</string>
    <string name="error_quantity">Quantity must be greater than 0</string>
    <string name="error_save_failed">Failed to save product: %1$s</string>

    <!-- Success Messages -->
    <string name="success_product_saved">Product saved successfully</string>

    <!-- Date Format Labels -->
    <string name="date_format_with_day">DD/MM/YYYY</string>
    <string name="date_format_without_day">MM/YYYY</string>
    <string name="mfg_date_with_day">Mfg Date (DD/MM/YYYY)</string>
    <string name="mfg_date_without_day">Mfg Date (MM/YYYY)</string>
    <string name="exp_date_with_day">Exp Date (DD/MM/YYYY)</string>
    <string name="exp_date_without_day">Exp Date (MM/YYYY)</string>

    <!-- About Screen -->
    <string name="about_title">About</string>
    <string name="features_title">Features</string>
    <string name="feature_tracking">Product Tracking System</string>
    <string name="feature_expiry">Expiry Date Management</string>
    <string name="feature_multi_branch">Multi-branch Support</string>
    <string name="feature_export">Data Export Capabilities</string>
    <string name="feature_updates">Automatic Updates</string>
    <string name="feature_interface">User-friendly Interface</string>

    <!-- AllItemsScreen strings -->
    <string name="total_active_products">Total Active Products: %1$d</string>
    <string name="no_results_for">No results found for \"%1$s\"</string>
    <string name="try_different_search">Try a different search term</string>
    <string name="no_products_found">No products found</string>

    <!-- SortOption display names -->
    <string name="sort_name_asc">Name (A to Z)</string>
    <string name="sort_name_desc">Name (Z to A)</string>
    <string name="sort_expiry_nearest">Expiry Date (Nearest)</string>
    <string name="sort_expiry_farthest">Expiry Date (Farthest)</string>
    <string name="sort_quantity_high">Quantity (High to Low)</string>
    <string name="sort_quantity_low">Quantity (Low to High)</string>
    <string name="sort_branch_asc">Branch (A to Z)</string>
    <string name="sort_branch_desc">Branch (Z to A)</string>

    <!-- Section Headers -->
    <string name="upcoming_features">Upcoming Features</string>

    <!-- Navigation Items -->
    <string name="nav_dashboard">Dashboard</string>
    <string name="nav_all_items">All Items</string>
    <string name="nav_new_entry">New Entry</string>
    <string name="nav_expired_list">Expired List</string>
    <string name="nav_export">Export</string>
    <string name="nav_settings">Settings</string>
    <string name="nav_about">About</string>

    <!-- Upcoming Features -->
    <string name="nav_analytics">Analytics</string>
    <string name="nav_attendance">Attendance</string>
    <string name="nav_incentive">Incentive</string>
    <string name="coming_soon">Coming Soon</string>

    <!-- Form Section Headers -->
    <string name="date_information">Date Information</string>

    <!-- Error Messages -->
    <string name="error_expiry_after_manufacturing">Expiry date must be after manufacturing date</string>
    <string name="error_invalid_batch_format">Invalid batch format. Use HH:MM format</string>
    <string name="error_invalid_mfg_date_format_with_day">Invalid manufacturing date format (DD/MM/YYYY)</string>
    <string name="error_invalid_mfg_date_format_without_day">Invalid manufacturing date format (MM/YYYY)</string>
    <string name="error_invalid_exp_date_format_with_day">Invalid expiry date format (DD/MM/YYYY)</string>
    <string name="error_invalid_exp_date_format_without_day">Invalid expiry date format (MM/YYYY)</string>
    <string name="error_invalid_date_format">Invalid date format</string>
    <string name="error_quantity_greater_than_zero">Quantity must be greater than zero</string>
    <string name="error_product_name_required">Product name is required</string>

    <!-- Threshold Menu Items -->
    <string name="nav_threshold">Threshold</string>
    <string name="threshold_title">Threshold Settings</string>
    <string name="threshold_description">Configure product quantity and expiry thresholds for notifications.</string>
    <string name="export_to_excel">Export to Excel</string>
    <string name="export_error">Export Error</string>
</resources>
{"formatVersion": 1, "database": {"version": 4, "identityHash": "0ac4d233d7f5f3721f5b2aeafe887459", "entities": [{"tableName": "products", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `branchName` TEXT NOT NULL, `productName` TEXT NOT NULL, `productType` TEXT NOT NULL, `barcode` TEXT NOT NULL, `batch` TEXT NOT NULL, `initialQuantity` INTEGER NOT NULL, `currentQuantity` INTEGER NOT NULL, `mfgDate` INTEGER NOT NULL, `expireDate` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `isExpired` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "branchName", "columnName": "branchName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "productName", "columnName": "productName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "productType", "columnName": "productType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "barcode", "columnName": "barcode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "batch", "columnName": "batch", "affinity": "TEXT", "notNull": true}, {"fieldPath": "initialQuantity", "columnName": "initialQuantity", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "currentQuantity", "columnName": "currentQuantity", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mfgDate", "columnName": "mfgDate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "expireDate", "columnName": "expireDate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isExpired", "columnName": "isExpired", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "product_catalog", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`barcode` TEXT NOT NULL, `productName` TEXT NOT NULL, `productType` TEXT NOT NULL, PRIMARY KEY(`barcode`))", "fields": [{"fieldPath": "barcode", "columnName": "barcode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "productName", "columnName": "productName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "productType", "columnName": "productType", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["barcode"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '0ac4d233d7f5f3721f5b2aeafe887459')"]}}
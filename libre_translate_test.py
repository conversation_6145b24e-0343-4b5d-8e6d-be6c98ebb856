import requests
import json

url = "https://libretranslate.de/translate"

payload = {
    "q": "Hello, world!",
    "source": "en",
    "target": "ar",
    "format": "text"
}

headers = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

try:
    response = requests.post(url, json=payload, headers=headers)
    print(f"Status Code: {response.status_code}")
    print("Response JSON:", response.json())
except Exception as e:
    print(f"Error: {str(e)}")
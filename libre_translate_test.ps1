# Define the request body as a JSON object
$body = @{
    q = "Hello, world!"
    source = "en"
    target = "ar"
    format = "text"
} | ConvertTo-Json

# Set the content type header
$headers = @{
    "Content-Type" = "application/json"
}

try {
    # Make the POST request
    $response = Invoke-RestMethod -Uri "https://libretranslate.de/translate" 
                             -Method Post 
                             -Headers $headers 
                             -Body $body 
                             -ErrorAction Stop
    
    # Display the response
    Write-Host "Status Code: 200 (Success)"
    Write-Host "Translation Result: $response.translatedText"
}
catch {
    # Handle any errors
    Write-Host "Error: $_"
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)"
    }
}
package rt.tt.temp.ui.viewmodels

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import rt.tt.temp.services.DriveFile
import rt.tt.temp.services.GoogleDriveService
import rt.tt.temp.data.AppDatabase
import rt.tt.temp.utils.BackupUtils
import android.util.Log
import com.google.gson.Gson
import rt.tt.temp.data.Product
import rt.tt.temp.MainActivity

class GoogleDriveViewModel(private val context: Context) : ViewModel() {
    private val driveService = GoogleDriveService(context)
    private val tag = "GoogleDriveViewModel"

    private val _isConnected = MutableStateFlow(false)
    val isConnected: StateFlow<Boolean> = _isConnected.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _lastSyncTime = MutableStateFlow<String?>(null)
    val lastSyncTime: StateFlow<String?> = _lastSyncTime.asStateFlow()

    private val _files = MutableStateFlow<List<DriveFile>>(emptyList())
    val files: StateFlow<List<DriveFile>> = _files.asStateFlow()

    private val _currentFolderId = MutableStateFlow<String?>(null)
    val currentFolderId: StateFlow<String?> = _currentFolderId.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _operationStatus = MutableStateFlow<String?>(null)
    val operationStatus: StateFlow<String?> = _operationStatus.asStateFlow()

    /**
     * Initialize Google Drive connection
     */
    fun initializeDriveConnection() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "Connecting to Google Drive..."

            try {
                // First, trigger Google Sign-In if we're in MainActivity
                if (context is MainActivity) {
                    context.startGoogleDriveSignIn { success ->
                        if (success) {
                            // After successful sign-in, initialize the drive service
                            viewModelScope.launch {
                                initializeDriveService()
                            }
                        } else {
                            _isConnected.value = false
                            _errorMessage.value = "Google Sign-In failed"
                            _operationStatus.value = null
                            _isLoading.value = false
                        }
                    }
                } else {
                    // If not in MainActivity, try to initialize directly
                    initializeDriveService()
                }
            } catch (e: Exception) {
                Log.e(tag, "Error starting Drive connection", e)
                _isConnected.value = false
                _errorMessage.value = "Connection error: ${e.message}"
                _operationStatus.value = null
                _isLoading.value = false
            }
        }
    }

    private suspend fun initializeDriveService() {
        try {
            _operationStatus.value = "Initializing Google Drive service..."

            val success = driveService.initialize()
            if (success) {
                _isConnected.value = true
                _operationStatus.value = "Creating app folders..."

                // Create app folder structure
                val folderSuccess = driveService.createAppFolderStructure()
                if (folderSuccess) {
                    _operationStatus.value = "Connected successfully"
                    // Load initial files
                    loadAppFiles()
                } else {
                    _operationStatus.value = "Connected (folder creation failed)"
                    Log.w(tag, "Connected but failed to create folder structure")
                }
            } else {
                _isConnected.value = false
                _errorMessage.value = "Failed to initialize Google Drive service. Please check your internet connection and try again."
                _operationStatus.value = null
            }
        } catch (e: SecurityException) {
            Log.e(tag, "Security error - missing permissions", e)
            _isConnected.value = false
            _errorMessage.value = "Permission denied. Please grant Google Drive access."
            _operationStatus.value = null
        } catch (e: Exception) {
            Log.e(tag, "Error initializing Drive service", e)
            _isConnected.value = false
            _errorMessage.value = when {
                e.message?.contains("network", true) == true -> "Network error. Please check your internet connection."
                e.message?.contains("auth", true) == true -> "Authentication failed. Please sign in again."
                else -> "Service initialization error: ${e.message}"
            }
            _operationStatus.value = null
        } finally {
            _isLoading.value = false
        }
    }

    /**
     * Disconnect from Google Drive
     */
    fun disconnect() {
        _isConnected.value = false
        _lastSyncTime.value = null
        _files.value = emptyList()
        _currentFolderId.value = null
        _operationStatus.value = "Disconnected"
    }

    /**
     * Load files from the app folder
     */
    fun loadAppFiles() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "Loading files..."

            try {
                val appFolderId = driveService.getAppFolderId()
                _currentFolderId.value = appFolderId

                val fileList = driveService.listFiles(appFolderId)
                _files.value = fileList
                _operationStatus.value = "Loaded ${fileList.size} files"
            } catch (e: Exception) {
                Log.e(tag, "Error loading files", e)
                _errorMessage.value = "Failed to load files: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Load files from a specific folder
     */
    fun loadFilesFromFolder(folderId: String?) {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "Loading folder contents..."

            try {
                _currentFolderId.value = folderId
                val fileList = driveService.listFiles(folderId)
                _files.value = fileList
                _operationStatus.value = "Loaded ${fileList.size} items"
            } catch (e: Exception) {
                Log.e(tag, "Error loading folder contents", e)
                _errorMessage.value = "Failed to load folder: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Create a new folder
     */
    fun createFolder(folderName: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "Creating folder..."

            try {
                val folder = driveService.createFolder(folderName, _currentFolderId.value)
                if (folder != null) {
                    _operationStatus.value = "Folder '$folderName' created successfully"
                    // Refresh the current folder view
                    loadFilesFromFolder(_currentFolderId.value)
                } else {
                    _errorMessage.value = "Failed to create folder"
                    _operationStatus.value = null
                }
            } catch (e: Exception) {
                Log.e(tag, "Error creating folder", e)
                _errorMessage.value = "Failed to create folder: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Upload a file to the current folder
     */
    fun uploadFile(fileName: String, content: ByteArray, mimeType: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "Uploading file..."

            try {
                val file = driveService.uploadFile(fileName, content, mimeType, _currentFolderId.value)
                if (file != null) {
                    _operationStatus.value = "File '$fileName' uploaded successfully"
                    // Refresh the current folder view
                    loadFilesFromFolder(_currentFolderId.value)
                } else {
                    _errorMessage.value = "Failed to upload file"
                    _operationStatus.value = null
                }
            } catch (e: Exception) {
                Log.e(tag, "Error uploading file", e)
                _errorMessage.value = "Failed to upload file: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Delete a file
     */
    fun deleteFile(fileId: String, fileName: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "Deleting file..."

            try {
                val success = driveService.deleteFile(fileId)
                if (success) {
                    _operationStatus.value = "File '$fileName' deleted successfully"
                    // Refresh the current folder view
                    loadFilesFromFolder(_currentFolderId.value)
                } else {
                    _errorMessage.value = "Failed to delete file"
                    _operationStatus.value = null
                }
            } catch (e: Exception) {
                Log.e(tag, "Error deleting file", e)
                _errorMessage.value = "Failed to delete file: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Download a file
     */
    fun downloadFile(fileId: String, fileName: String): ByteArray? {
        var result: ByteArray? = null
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "Downloading file..."

            try {
                result = driveService.downloadFile(fileId)
                if (result != null) {
                    _operationStatus.value = "File '$fileName' downloaded successfully"
                } else {
                    _errorMessage.value = "Failed to download file"
                    _operationStatus.value = null
                }
            } catch (e: Exception) {
                Log.e(tag, "Error downloading file", e)
                _errorMessage.value = "Failed to download file: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
        return result
    }

    /**
     * Backup inventory data to Google Drive
     */
    fun backupInventoryData() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "Creating backup..."

            try {
                val database = AppDatabase.getInstance(context)
                val products = database.productDao().getAllProducts()

                // Convert products to JSON
                val gson = Gson()
                val jsonData = gson.toJson(products)

                val success = driveService.backupInventoryData(jsonData)
                if (success) {
                    val currentTime = java.text.SimpleDateFormat("MMM dd, yyyy HH:mm", java.util.Locale.getDefault())
                        .format(java.util.Date())
                    _lastSyncTime.value = currentTime
                    _operationStatus.value = "Backup completed successfully"

                    // Refresh files if we're in the backup folder
                    loadAppFiles()
                } else {
                    _errorMessage.value = "Failed to create backup"
                    _operationStatus.value = null
                }
            } catch (e: Exception) {
                Log.e(tag, "Error creating backup", e)
                _errorMessage.value = "Backup failed: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Restore inventory data from a backup file
     */
    fun restoreFromBackup(fileId: String, fileName: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "Restoring from backup..."

            try {
                val content = driveService.downloadFile(fileId)
                if (content != null) {
                    val jsonData = String(content)
                    val gson = Gson()
                    val products = gson.fromJson(jsonData, Array<Product>::class.java).toList()

                    // Clear existing data and insert restored data
                    val database = AppDatabase.getInstance(context)
                    database.productDao().deleteAll()
                    database.productDao().insertAll(products)

                    _operationStatus.value = "Restored ${products.size} products from '$fileName'"
                } else {
                    _errorMessage.value = "Failed to download backup file"
                    _operationStatus.value = null
                }
            } catch (e: Exception) {
                Log.e(tag, "Error restoring from backup", e)
                _errorMessage.value = "Restore failed: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Load backup files
     */
    fun loadBackupFiles() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "Loading backup files..."

            try {
                val backupFiles = driveService.getBackupFiles()
                _files.value = backupFiles
                _operationStatus.value = "Found ${backupFiles.size} backup files"
            } catch (e: Exception) {
                Log.e(tag, "Error loading backup files", e)
                _errorMessage.value = "Failed to load backup files: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * Clear operation status
     */
    fun clearStatus() {
        _operationStatus.value = null
    }
}
